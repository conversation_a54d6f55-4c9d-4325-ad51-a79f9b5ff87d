<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="id" style="width: 80px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.subject" placeholder="标题查询" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.type" placeholder="类型" clearable style="width: 80px" class="filter-item">
        <el-option :key="2" label="群聊" :value="2" />
        <el-option :key="1" label="单聊" :value="1" />
      </el-select>
      <el-tooltip class="item" effect="dark" content="输入用户id,使用英文逗号分隔,例如:1,2" placement="top-start">
        <el-input v-model="listQuery.singleChat" placeholder="单聊查询" size="normal" clearable style="width: 150px;" class="filter-item" />
      </el-tooltip>
      <el-select v-model="listQuery.hasTag" placeholder="是否已设标签" clearable style="width: 150px" class="filter-item">
        <el-option :key="1" label="是" :value="true" />
        <el-option :key="2" label="否" :value="false" />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-edit" @click="batchSetTag">
        批量打标签
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        align="center"
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="ID" width="85">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="标题">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.subject, $event)">{{ row.subject }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建人">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.creatorInfo.nickname, $event)">{{ row.creatorInfo.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="群主">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.adminInfo.nickname, $event)">{{ row.adminInfo.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="标签1">
        <template slot-scope="{row}">
          <el-select v-model="row.tag1" placeholder="大区" clearable filterable @change="handleSelect($event, 1, row)">
            <el-option
              v-for="item in regionOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" label="标签2">
        <template slot-scope="{row}">
          <el-select v-model="row.tag2" placeholder="分公司" clearable filterable @change="handleSelect($event, 2, row)">
            <el-option
              v-for="item in branchOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" label="标签3">
        <template slot-scope="{row}">
          <el-autocomplete
            v-model="row.tag3"
            :fetch-suggestions="querySearchTags"
            placeholder="请输入"
            @select="handleSelect($event, 3, row)"
            @blur="handleBlur($event, row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="标签4">
        <template slot-scope="{row}">
          <el-select v-model="row.tag4" placeholder="类型" clearable filterable @change="handleSelect($event, 4, row)">
            <el-option key="人用内部" label="人用内部" value="人用内部" />
            <el-option key="人用外部" label="人用外部" value="人用外部" />
            <el-option key="兽用" label="兽用" value="兽用" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="type" width="70" align="center">
        <template slot-scope="{row}">
          {{ row.type| typeFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" width="100">
        <template slot-scope="{row}">
          <span>{{ row.creation_ts| formatTime }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="updated_at" label="更多" width="120">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row.id)">群成员列表</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="群成员列表"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-table
        v-loading="listLoading"
        :data="groupAttendees"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <el-table-column align="center" label="ID" width="80">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="登录名">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.login_name, $event)">{{ row.login_name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="昵称">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.nickname, $event)">{{ row.nickname }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="手机号">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.cellphone, $event)">{{ row.cellphone }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="邮箱">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.email, $event)">{{ row.email }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="角色" width="95">
          <template slot-scope="{row}">
            {{ row.role|roleFilter }}
          </template>
        </el-table-column>
        <el-table-column class-name="status-col" label="状态" width="90" align="center">
          <template slot-scope="{row}">
            {{ row.status|statusFilter }}
          </template>
        </el-table-column>
        <el-table-column class-name="status-col" label="类型" width="60" align="center">
          <template slot-scope="{row}">
            {{ row.type|userTypeFilter }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" width="100">
          <template slot-scope="{row}">
            <span>{{ row.creation_ts| formatTime }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      title="批量打标签"
      :visible.sync="tagDialogVisible"
      width="60%"
    >
      <el-form ref="form" label-width="80px" :inline="false" size="normal">
        <el-form-item label="标签1">
          <el-select v-model="tag1" placeholder="大区" clearable filterable>
            <el-option
              v-for="item in regionOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签2">
          <el-select v-model="tag2" placeholder="分公司" clearable filterable @change="handleSelect($event, 2, row)">
            <el-option
              v-for="item in branchOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签3">
          <el-autocomplete
            v-model="tag3"
            :fetch-suggestions="querySearchTags"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="标签4">
          <el-select v-model="tag4" placeholder="类型" clearable filterable @change="handleSelect($event, 4, row)">
            <el-option key="人用内部" label="人用内部" value="人用内部" />
            <el-option key="人用外部" label="人用外部" value="人用外部" />
            <el-option key="兽用" label="兽用" value="兽用" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tagDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="batchSetTagRequest">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getGroupAttendList, saveGroupTag } from '@/api/group'
import { searchTag } from '@/api/tag'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'
import { invertMap } from '@/utils/index'
import userConstant from '@/constant/user'
const { role, status } = userConstant
import regionBranch from '@/constant/branchAndRegion'
const { regionOptions, branchOptions } = regionBranch

export default {
  name: 'Group',
  components: { Pagination },

  filters: {
    userTypeFilter(type) {
      const typeMap = {
        1: '普通',
        2: '内部',
        3: '迈动'
      }
      return typeMap[type]
    },
    typeFilter(status) {
      const map = {
        2: '群聊',
        1: '单聊'
      }
      return map[status]
    },
    roleFilter(val) {
      const roleMap = invertMap(role)
      return roleMap[val]
    },
    statusFilter(val) {
      const statusMap = invertMap(status)
      return statusMap[val]
    },
    internalFilter(val) {
      return val ? '是' : '否'
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        id: null,
        subject: null,
        type: null,
        singleChat: '',
        hasTag: null
      },
      selectIds: [],
      dialogVisible: false,
      listLoading: true,
      groupAttendees: [],
      tag1: '',
      tag2: '',
      tag3: '',
      tag4: '',
      tagDialogVisible: false,
      regionOptions,
      branchOptions
    }
  },
  created() {
    if (this.$route.query.id) {
      this.listQuery.id = this.$route.query.id
    }
    this.handleFilter()
  },
  methods: {
    handleSelectionChange(groupList) {
      debugger
      this.selectIds = groupList.map((group) => group.id)
    },
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      getList(this.listQuery).then(response => {
        response.data.list.forEach((item) => {
          if (item.groupTag) {
            for (const tag of item.groupTag) {
              item[`tag${tag.sort}`] = tag.tagInfo?.caption
            }
          }
        })
        this.list = Object.assign([], response.data.list)
        this.total = response.data.count
        this.listLoading = false
      })
    },
    async showDialog(groupId) {
      const groupAttendeeList = await getGroupAttendList(groupId)
      this.groupAttendees = groupAttendeeList.data.map(item => item.attendee)
      this.dialogVisible = true
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    },
    async querySearchTags(searchTxt, cb) {
      const res = await searchTag({ caption: searchTxt, sort: 3 })
      const tag = res.data.list.map((tag) => {
        return { value: tag.caption }
      })
      cb(tag)
    },
    handleSelect(val, index, row) {
      console.log('handleSelect', val, index)
      const data = { groupIds: [row.id], tags: [row.tag1, row.tag2, row.tag3, row.tag4] }
      saveGroupTag(data)
    },
    handleBlur(event, row) {
      console.log('Event object:', event.target.value, row)
      const data = { groupIds: [row.id], tags: [row.tag1, row.tag2, row.tag3] }
      saveGroupTag(data)
    },
    batchSetTag() {
      if (this.selectIds.length === 0) {
        this.$message('请先选择群组')
      } else {
        this.tag1 = ''
        this.tag2 = ''
        this.tag3 = ''
        this.tagDialogVisible = true
      }
    },
    batchSetTagRequest() {
      const data = { groupIds: this.selectIds, tags: [this.tag1, this.tag2, this.tag3, this.tag4] }
      saveGroupTag(data).then(() => {
        this.tagDialogVisible = false
        this.findList()
        this.$message({
          message: '成功',
          type: 'success'
        })
      })
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
