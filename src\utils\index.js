import _ from 'lodash'
import moment from 'moment'

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
* @function 获取几天前0点的时间戳
* @param {Number} day 天数
*/
export function getBeforeTimeStampByDay(date, day) {
  const time = new Date(moment(date).subtract(Number(day), 'days')).setHours(0, 0, 0, 0) // 设为0点
  return time
}

export function transformMapToOption(dataMap) {
  const arr = []
  for (const key of Object.keys(dataMap)) {
    arr.push({ value: dataMap[key], label: key })
  }
  return arr
}

export function invertMap(dataMap) {
  return _.invert(dataMap)
}

export function emptyStrToNull(str) {
  if (str === '') {
    return null
  } else {
    return str
  }
}
