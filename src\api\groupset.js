import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/groupset/list',
    method: 'get',
    params
  })
}

export function getGroupsetAttendList(groupsetId) {
  return request({
    url: '/groupset/attendees',
    method: 'get',
    params: { groupsetId }
  })
}

export function saveGroupTag(params) {
  return request({
    url: '/groupset/tag',
    method: 'put',
    data: params
  })
}
