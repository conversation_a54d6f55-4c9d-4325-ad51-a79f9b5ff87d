<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="id" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.login_name" placeholder="登录名" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.nickname" placeholder="用户昵称" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.cellphone" placeholder="手机号" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.role" placeholder="角色" clearable style="width: 130px" class="filter-item">
        <el-option :key="0" label="无" :value="0" />
        <el-option :key="1" label="普通成员" :value="1" />
        <el-option v-if="role===3" :key="2" label="管理员" :value="2" />
        <el-option v-if="role===3" :key="3" label="超级管理员" :value="3" />
        <el-option :key="4" label="主任" :value="4" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.type" placeholder="类型" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="listQuery.timeScope"
        class="filter-item"
        type="daterange"
        align="right"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-dropdown v-if="role===3" @command="setUserType">
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-edit">
          批量设置用户类型
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">普通</el-dropdown-item>
          <el-dropdown-item command="2">内部</el-dropdown-item>
          <el-dropdown-item command="3">迈动</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        align="center"
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="ID" width="100">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="登录名">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.login_name, $event)">{{ row.login_name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="昵称">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.nickname, $event)">{{ row.nickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="手机号">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.cellphone, $event)">{{ row.cellphone }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="邮箱">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.email, $event)">{{ row.email }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="推荐人">
        <template slot-scope="{row}">
          <el-button v-if="row.referral_by" type="success" size="mini" @click="showReferral(row.referral_by)">{{ row['referral.nickname'] }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="角色" width="95">
        <template slot-scope="{row}">
          {{ row.role|roleFilter }}
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="状态" width="90" align="center">
        <template slot-scope="{row}">
          {{ row.status| statusFilter }}
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="类型" width="60" align="center">
        <template slot-scope="{row}">
          {{ row.type| typeFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" width="100">
        <template slot-scope="{row}">
          <span>{{ row.creation_ts| formatTime }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="updated_at" label="更多" width="100">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="updateUser(row)">操作</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="修改用户数据"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-form ref="userForm" :model="userForm" label-position="left">
        <el-form-item label="角色" size="normal">
          <el-select v-model="userForm.role" placeholder="状态" style="width: 130px" class="filter-item"> <!--当前用户不是超管则不能给别人设置角色-->
            <el-option :key="0" label="无" :value="0" />
            <el-option :key="1" label="普通成员" :value="1" />
            <el-option v-if="role===3" :key="2" label="管理员" :value="2" />
            <el-option v-if="role===3" :key="3" label="超级管理员" :value="3" />
            <el-option :key="4" label="主任" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" size="normal">
          <el-select v-model="userForm.status" placeholder="状态" style="width: 130px" class="filter-item">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="role===3" label="类型" size="normal">
          <el-select v-model="userForm.type" placeholder="类型" style="width: 130px" class="filter-item">
            <el-option :key="1" label="普通" :value="1" />
            <el-option :key="2" label="内部" :value="2" />
            <el-option :key="3" label="迈动" :value="3" />
          </el-select>
        </el-form-item>
        <div class="dialog-footer">
          <el-button type="primary" size="default" @click="submitUpdateUser()">确定</el-button>
          <el-button type="primary" size="default" @click="dialogVisible = false;userForm = {}">取消</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getList, updateUser, setMultiUserType } from '@/api/user'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'
import userConstant from '@/constant/user'
import { transformMapToOption, invertMap } from '@/utils/index'
import moment from 'moment'
const { role, status, type } = userConstant

const roleOptions = transformMapToOption(role)
const statusOptions = transformMapToOption(status)
const typeOptions = transformMapToOption(type)
export default {
  name: 'User',
  components: { Pagination },

  filters: {
    typeFilter(type) {
      const typeMap = {
        1: '普通',
        2: '内部',
        3: '迈动'
      }
      return typeMap[type]
    },
    roleFilter(val) {
      const roleMap = invertMap(role)
      return roleMap[val]
    },
    statusFilter(val) {
      const statusMap = invertMap(status)
      return statusMap[val]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        id: null,
        login_name: null,
        nickname: null,
        role: null,
        status: null,
        desc: 'creation_ts',
        timeScope: [moment().subtract(1, 'months').startOf('day'), moment().endOf('day')] // 最近一个月
      },
      selectUserId: [],
      userForm: { status: 0, role: 0 },
      roleOptions,
      statusOptions,
      typeOptions,
      dialogVisible: false,
      listLoading: true,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 7
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 30
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 90
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'role'
    ])
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      this.findList()
    },
    handleSelectionChange(userList) {
      this.selectUserId = userList.map((user) => user.id)
    },
    findList() {
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    updateUser(data) {
      this.dialogVisible = true
      this.userForm = Object.assign({}, data)
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    },
    submitUpdateUser() {
      updateUser(this.userForm).then(() => {
        this.$message({
          message: '成功',
          type: 'success'
        })
        this.dialogVisible = false
        this.handleFilter()
      })
    },
    async setUserType(type) {
      if (!this.selectUserId.length) {
        this.$message({ message: '请选择用户', type: 'warning' })
      } else {
        await setMultiUserType(this.selectUserId, type)
        this.handleFilter()
      }
    },
    async showReferral(id) {
      this.listQuery.id = id
      await this.handleFilter()
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}

</style>
