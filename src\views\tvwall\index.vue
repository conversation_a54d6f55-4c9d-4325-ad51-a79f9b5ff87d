<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.monitor_id" placeholder="主任id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.user_id" placeholder="用户id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.device_id" placeholder="设备id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="直播状态" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="95">
        <template slot-scope="{row}">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="channelId" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.channelId, $event)">{{ row.channelId }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="主任ids" align="center">
        <template slot-scope="{row}">
          <span>{{ row.monitorIds }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户id" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.group_id, $event)">{{ row.group_id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="设备id" align="center">
        <template slot-scope="{row}">
          {{ row }}
        </template>
      </el-table-column>
      <el-table-column label="声网uid" align="center">
        <template slot-scope="{row}">
          {{ row }}
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="直播状态" width="110" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | tagTypeFilter">{{ row.status| statusFilter }}</el-tag>
        </template>
      </el-table-column>
      <!--
      <el-table-column align="center" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row)">查看详情</el-button>
            <el-button v-if="row.status" type="danger" size="mini" @click="forceClose(row.channelId)">强制关闭直播间</el-button>
            <el-button v-if="row.status" type="danger" size="mini" @click="watchLive(row.channelId, row.group_id)">访客观看直播</el-button>
          </div>
        </template>
      </el-table-column> -->
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="展示数据"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-button type="primary" size="mini" @click="onClipboard(jsonData, $event)">点击复制</el-button>
      <div class="editor-container" />
    </el-dialog>
  </div>
</template>

<script>
import { getList } from '@/api/tvwall'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'

const statusOptions = [
  {
    label: '已结束',
    value: 0
  },
  {
    label: '直播中',
    value: 1
  }
]
export default {
  name: 'AgoraChannel',
  components: { Pagination },

  filters: {
    tagTypeFilter(type) {
      const typeMap = {
        0: 'info',
        1: 'danger'
      }
      return typeMap[type]
    },
    statusFilter(status) {
      const statusMap = {
        0: '已结束',
        1: '直播中'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        channelId: null,
        monitor_id: null,
        group_id: null,
        device_id: null,
        user_id: null,
        status: null
      },
      statusOptions,
      dialogVisible: false,
      listLoading: true
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    showDialog(data) {
      this.dialogVisible = true
      this.jsonData = data
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof text === 'number') {
        text = `${text}`
      }
      clip(text, event) // 只能复制字符串
    },
    watchLive(channelId, groupId) {
      const decodePath = window.btoa(`channel_id=${channelId}#####group_id=${groupId}`)
      const baseUrl = window.location.host.includes('service-consult') ? 'https://consult.mindray.com' : 'https://consult-dev.mindray.com'
      const url = `${baseUrl}/pc/ultrasync_pc.html#/webLive/${decodePath}`
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
