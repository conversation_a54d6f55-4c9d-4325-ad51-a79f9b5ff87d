<template>
  <div>
    <template v-if="isShowTips">
      <el-alert
        id="tips"
        title="提示"
        type="info"
        description="将会以 压缩包(.zip) 形式导出"
        show-icon
        :closable="false"
      />
    </template>
    <div>
      <el-checkbox
        v-if="isShowAll"
        id="checkAll"
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全选
      </el-checkbox>
    </div>
    <div>
      <el-checkbox-group v-model="checkedTypes" @change="handleCheckedTypesChange">
        <el-checkbox
          v-for="exportType in exportTypes"
          :key="exportType"
          :min="1"
          :label="exportType"
        >
          {{ exportType }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <el-collapse v-if="isShowMoreOptions" accordion>
      <el-collapse-item>
        <template slot="title">
          高级选项&nbsp;
          <i class="header-icon el-icon-set-up">
            <!-- 图标位 -->
          </i>
        </template>
        <div
          v-for="exportType in shiftedCheckedTypes"
          :key="exportType"
        >
          <div v-show="checkedTypes.indexOf(exportType) !== -1">
            <span>{{ exportType }} (至少选择一项): </span>
            <el-checkbox-group v-model="checkedMoreOptions[exportType]" :min="1" @change="flushRender()">
              <el-checkbox label="CN" />
              <el-checkbox label="EN" />
              <el-checkbox label="ES" />
              <el-checkbox label="PTBR" />
              <el-checkbox label="RU" />
              <el-checkbox label="DE" />
              <el-checkbox label="IT" />
              <el-checkbox label="FR" />
            </el-checkbox-group>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
export default {
  name: 'ExportCheckbox',
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkAll: false,
      checkedTypes: [],
      checkedMoreOptions: {},
      moreOptions: ['CN', 'EN', 'ES', 'PTBR', 'RU', 'DE', 'IT', 'FR'],
      exportTypes: this.options,
      isIndeterminate: true
    }
  },
  computed: {
    shiftedCheckedTypes: function() {
      const tmp = JSON.parse(JSON.stringify(this.checkedTypes))
      if (tmp[0] === 'Excel') {
        tmp.shift()
      }
      return tmp
    },
    isShowTips: function() {
      const length = this.checkedTypes.length
      if (length >= 2 || this.checkedTypes[0] !== 'Excel') {
        if (length === 0) {
          return false
        }
        return true
      } else {
        return false
      }
    },
    isShowMoreOptions: function() {
      const length = this.checkedTypes.length
      if (length >= 2 || this.checkedTypes[0] !== 'Excel') {
        if (length === 0) {
          return false
        }
        return true
      } else {
        return false
      }
    },
    isShowAll: function() {
      if (this.options.length > 1) {
        return true
      }
      return false
    }
  },
  watch: {
    // 监听响应式
    checkedTypes: function(newVal, oldVal) {
      this.$emit('update', this.checkedTypes)
    }
  },
  created() {
    for (const i in this.options) {
      if (this.options[i] !== 'Excel') {
        // 使用set进行响应式处理
        this.$set(this.checkedMoreOptions, String(this.options[i]), ['CN', 'EN', 'ES', 'PTBR', 'RU', 'DE', 'IT', 'FR'])
      }
    }
    this.$emit('update', this.checkedTypes)
    this.$emit('option', this.checkedMoreOptions)
  },
  methods: {
    flushRender() {
      this.$forceUpdate()
    },
    handleCheckAllChange(val) {
      this.checkedTypes = val ? this.options : []
      this.isIndeterminate = false
    },
    handleCheckedTypesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.exportTypes.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.exportTypes.length
    }
  }
}
</script>
<style lang="scss" scoped>
  .header-icon {
    margin-left: 0.25rem;
    vertical-align: top;
    font-size: larger;
  }
  .el-collapse {
    margin-top:1rem;
    margin-bottom: 1rem;
  }

  #checkAll {
    margin-bottom: 1rem;
  }

  #tips {
    margin-bottom: 1.5rem;
  }
</style>
