<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.device_id" placeholder="设备序列号" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.device_type" placeholder="类型" clearable style="width: 130px" class="filter-item">
        <el-option :key="6" label="多普勒" :value="6" />
        <el-option :key="7" label="盒子" :value="7" />
        <el-option :key="9" label="灵珂" :value="9" />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="primary" @click="dialogVisible = true">上传灵珂名单</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="85">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备id">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.device_id, $event)">{{ row.device_id }}</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="设备名">
        <template slot-scope="{row}">
          {{ row.device_name }}
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="设备类型">
        <template slot-scope="{row}">
          {{ row.device_type|deviceTypeFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="激活状态">
        <template slot-scope="{row}">
          {{ row|licenseFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" width="100">
        <template slot-scope="{row}">
          <span>{{ row.created_at| formatTime }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="updated_at" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row)">查看更多</el-button>
            <el-button v-if="row.license" type="primary" size="mini" @click="cancelActive(row.device_id)">取消激活</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="上传灵珂设备序列号Excel文件"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        :action="action"
        :file-list="fileList"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
      >
        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        <el-button type="success" size="small" style="margin-left: 10px;" @click="submitUpload">确定上传</el-button>
      </el-upload>
    </el-dialog>
    <el-dialog
      title="展示数据"
      :visible.sync="jsonDialogVisible"
      width="73%"
    >
      <el-button type="primary" size="mini" @click="onClipboard(jsonData, $event)">点击复制</el-button>
      <div class="editor-container">
        <json-viewer
          :value="jsonData"
        />
        <el-button v-if="selectDevice.device_type === 9 && nickname.includes('huangweilong') && !selectDevice.license" type="primary" size="mini" @click="active()">激活</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, importPassUlink, cancelDeviceLicense, activeDeviceLicense } from '@/api/device'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'
import * as XLSX from 'xlsx'
import JsonViewer from 'vue-json-viewer'

export default {
  name: 'Device',
  components: { Pagination, JsonViewer },

  filters: {
    deviceTypeFilter(type) {
      const map = {
        6: '多普勒',
        7: '盒子',
        9: '灵珂'
      }
      return map[type]
    },
    licenseFilter(row) {
      if (row.device_type === 9) {
        return row.license ? '已激活' : '未激活'
      } else {
        return '无'
      }
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        id: null,
        device_id: null,
        device_type: null
      },
      fileList: [],
      dialogVisible: false,
      jsonDialogVisible: false,
      listLoading: true,
      jsonData: {},
      action: `${window.location.host}/device/import`,
      selectDevice: {},
      nickname: this.$store.state.user.nickname
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    showDialog(device) {
      this.jsonDialogVisible = true
      this.jsonData = device.more_details
      this.selectDevice = device
    },
    alertSuccessMsg() {
      this.$message({
        message: '成功',
        type: 'success'
      })
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    },
    readXLSX(file) {
      const nameSplit = file.name.split('.')
      const format = nameSplit[nameSplit.length - 1]
      if (!['xlsx', 'csv'].includes(format)) {
        return false
      }
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const results = XLSX.utils.sheet_to_json(worksheet)
          resolve(results)
        }
        reader.readAsArrayBuffer(file)
      })
    },
    async beforeUpload(file) {
      this.excelList = [file]
      const results = await this.readXLSX(file) // 读取到的内容
      const deviceIdAndSn = []
      for (const item of results) {
        deviceIdAndSn.push({ device_id: item['部件SN号'], mindray_sn: item['设备号'] })
      }
      await importPassUlink(deviceIdAndSn)
    },
    uploadSuccess() {
      this.alertSuccessMsg()
      setTimeout(() => {
        this.fileList = []
        this.dialogVisible = false
        this.handleFilter()
      }, 500)
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    async cancelActive(device_id) {
      this.$confirm('是否取消激活', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await cancelDeviceLicense(device_id)
        this.findList()
        this.$message({
          type: 'success',
          message: '取消成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    active() {
      this.$confirm('是否激活', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await activeDeviceLicense(this.selectDevice.device_id)
        this.findList()
        this.jsonDialogVisible = false
        this.$message({
          type: 'success',
          message: '成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
