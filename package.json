{"name": "mindray-ui", "version": "4.4.0", "description": "云++后台", "author": "hwl", "scripts": {"dev": "node -e \"process.platform === 'win32' ? require('child_process').execSync('set NODE_ENV=dev && vue-cli-service serve --mode local', {stdio: 'inherit'}) : require('child_process').execSync('NODE_ENV=dev vue-cli-service serve --mode local', {stdio: 'inherit'})\"", "build": "vue-cli-service build", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"ali-oss": "^6.21.0", "axios": "0.18.1", "clipboard": "^2.0.11", "codemirror": "^5.45.0", "core-js": "^3.6.5", "echarts": "^5.4.1", "element-ui": "^2.15.12", "hls.js": "^1.6.2", "js-cookie": "2.2.0", "jsonlint": "^1.6.3", "lodash": "^4.17.21", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "plyr": "^3.7.8", "script-loader": "^0.7.2", "spark-md5": "^3.0.2", "vue": "2.6.10", "vue-json-viewer": "^2.2.22", "vue-router": "3.6.5", "vuescroll": "^4.18.1", "vuex": "3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}