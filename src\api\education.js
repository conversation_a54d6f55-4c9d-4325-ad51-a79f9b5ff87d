import request from '@/utils/request'

function getAdminList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

function getProjectList(data) {
  return request({
    url: '/training/list',
    method: 'post',
    data
  })
}

function createProject(data) {
  return request({
    url: '/training/create',
    method: 'post',
    data
  })
}

function updateProject(data) {
  return request({
    url: '/training/update',
    method: 'post',
    data
  })
}

function updateStatus(data) {
  return request({
    url: '/training/update/status',
    method: 'post',
    data
  })
}

function getAssessDetail(data) {
  return request({
    url: '/training/test/info',
    method: 'post',
    data
  })
}

function assessmentConfig(data) {
  return request({
    url: '/training/test/update',
    method: 'post',
    data
  })
}

function updateAssessPaper(data) {
  return request({
    url: '/training/test/testUpdatePaperInfo',
    method: 'post',
    data
  })
}

function getCountryList() {
  return request({
    url: '/training/countryList',
    method: 'post'
  })
}

const eduService = {
  getAdminList,
  getProjectList,
  createProject,
  updateProject,
  updateStatus,
  getAssessDetail,
  getCountryList,
  assessmentConfig,
  updateAssessPaper
}

export { eduService }

