/**
 * <PERSON> 2023.7.25
 */

'use strict'

import request from '@/utils/request'
import { keyDataJSONize } from '@/utils/multilingual'
// import axios from 'axios'
// import store from '@/store'
// import { getToken } from '@/utils/auth'

export function getList(params) {
  return request({
    url: '/multilingual/getList',
    method: 'get',
    params
  })
}

export function updateMultilingual(val) {
  const data = keyDataJSONize(val)
  return request({
    url: '/multilingual/update',
    method: 'put',
    data
  })
}

export function updateMultilingualNeedTranslate(val) {
  const data = val
  return request({
    url: '/multilingual/updateNeedTranslate',
    method: 'put',
    data
  })
}

export function getTranslationNow(translateText, from, to) {
  return request({
    url: '/multilingual/getTranslate',
    method: 'get',
    params: {
      translateText, from, to
    }
  })
}

export function insertMultilingual(val) {
  const data = keyDataJSONize(val)
  return request({
    url: '/multilingual/insert',
    method: 'post',
    data
  })
}

export function deleteMultilingual(data) {
  return request({
    url: '/multilingual/delete',
    method: 'delete',
    data
  })
}

export function uploadMultilingual(data) {
  return request({
    url: '/multilingual/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryVCFSAonTuDbVCoAN'
    }
  })
}

export function uploadMultilingualByPlatforms(data) {
  return request({
    url: '/multilingual/uploadPlatforms',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryVCFSAonTuDbVCoAN'
    }
  })
}

export async function exportMultilingual(list, ...params) {
  let options = null
  if (params) {
    options = params[0]
  }
  return request({
    url: `/multilingual/export?list=${JSON.stringify(list)}&options=${JSON.stringify(options)}`,
    method: 'get',
    responseType: 'blob',
    params
  })
}

export async function tsLocationExportMultilingual(data) {
  return request({
    url: '/multilingual/tsLocationExport',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryVCFSAonTuDbVCoAN'
    }
  })
}

export async function getSampleExcel(params) {
  return request({
    url: '/multilingual/getSample',
    method: 'get',
    responseType: 'blob',
    params
  })
}
