<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.channelId" placeholder="channelId" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.groupSubject" placeholder="群标题" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.group_id" placeholder="群id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="直播状态" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <!-- <el-button v-waves :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download" @click="handleDownload">
        导出
      </el-button> -->
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="95">
        <template slot-scope="{row}">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="channelId" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.channelId, $event)">{{ row.channelId }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="群标题" align="center">
        <template slot-scope="{row}">
          <span>{{ row.groupInfo.subject }}</span>
        </template>
      </el-table-column>
      <el-table-column label="群id" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.group_id, $event)">{{ row.group_id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="主持人" align="center">
        <template slot-scope="{row}">
          {{ row.hostInfo.nickname }}
        </template>
      </el-table-column>
      <el-table-column label="主持人id" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.host_uid, $event)">{{ row.host_uid }}</el-button>
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="直播状态" width="110" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | tagTypeFilter">{{ row.status| statusFilter }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="录制状态" width="110" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.recording_status | tagTypeFilter">{{ row.recording_status| recordingStatusFilter }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showChannelUser(row.channelId)">查看详情</el-button>
            <el-button v-if="row.status" type="danger" size="mini" @click="forceClose(row.channelId)">强制关闭直播间</el-button>
            <el-button v-if="row.status" type="danger" size="mini" @click="watchLive(row.channelId, row.group_id)">访客观看直播</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="直播间成员"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-table
        :data="channelUserList"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="channelId" align="center">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.channelId, $event)">{{ row.channelId }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="声网id" align="center">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.agora_uid, $event)">{{ row.agora_uid }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="用户id" align="center">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.user_id, $event)">{{ row.user_id }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="用户昵称" align="center">
          <template slot-scope="{row}">
            <span>{{ row.userInfo.nickname }}</span>
          </template>
        </el-table-column>
        <el-table-column label="摄像头状态" align="center">
          <template slot-scope="{row}">
            <span>{{ row.video|videoAudioFilter }}</span>
          </template>
        </el-table-column>
        <el-table-column label="麦克风状态" align="center">
          <template slot-scope="{row}">
            <span>{{ row.audio|videoAudioFilter }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center">
          <template slot-scope="{row}">
            <span>{{ row.stream_type|streamTypeFilter }}</span>
          </template>
        </el-table-column>
        <el-table-column label="updateAt" align="center">
          <template slot-scope="{row}">
            <span>{{ row.updated_at|formatTime }}</span>
          </template>
        </el-table-column>
        <el-table-column class-name="status-col" label="直播状态" width="110" align="center">
          <template slot-scope="{row}">
            <el-tag :type="row.status | tagTypeFilter">{{ row.status| statusFilter }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getChannelUserList } from '@/api/channel'
import { closeChannel } from '@/api/agora'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'

const statusOptions = [
  {
    label: '已结束',
    value: 0
  },
  {
    label: '直播中',
    value: 1
  }
]
export default {
  name: 'AgoraChannel',
  components: { Pagination },

  filters: {
    tagTypeFilter(type) {
      const typeMap = {
        0: 'info',
        1: 'danger'
      }
      return typeMap[type]
    },
    statusFilter(status) {
      const statusMap = {
        0: '已结束',
        1: '直播中'
      }
      return statusMap[status]
    },
    recordingStatusFilter(status) {
      const statusMap = {
        0: '未录制',
        1: '录制中'
      }
      return statusMap[status]
    },
    whiteboardStatusFilter(status) {
      const statusMap = {
        0: '未开启',
        1: '开启中'
      }
      return statusMap[status]
    },
    videoAudioFilter(value) {
      return value ? '开启' : '关闭'
    },
    streamTypeFilter(value) {
      return value ? '主流' : '辅流'
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        channelId: null,
        group_id: null,
        groupSubject: null,
        status: null
      },
      statusOptions,
      dialogVisible: false,
      listLoading: true,
      jsonData: {},
      channelUserList: null
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    showChannelUser(channelId) {
      getChannelUserList(channelId).then(response => {
        this.dialogVisible = true
        this.channelUserList = response.data
      })
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof text === 'number') {
        text = `${text}`
      }
      clip(text, event) // 只能复制字符串
    },
    forceClose(channelId) {
      this.$confirm('请确认此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        closeChannel({ channelId }).then((res) => {
          if (res.code === 200) {
            this.handleFilter()
            this.$message({
              type: 'success',
              message: '关闭成功'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    watchLive(channelId, groupId) {
      const decodePath = window.btoa(`channel_id=${channelId}#####group_id=${groupId}`)
      const baseUrl = window.location.host.includes('service-consult') ? 'https://consult.mindray.com' : 'https://consult-dev.mindray.com'
      const url = `${baseUrl}/pc/ultrasync_pc.html#/webLive/${decodePath}`
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
