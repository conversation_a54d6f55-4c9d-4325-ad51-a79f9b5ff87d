import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/device/list',
    method: 'get',
    params
  })
}

export function importPassUlink(deviceIdAndSn) {
  return request({
    url: '/device/import',
    method: 'post',
    data: { deviceIdAndSn }
  })
}

export function cancelDeviceLicense(device_id) {
  return request({
    url: '/device/cancel/license',
    method: 'put',
    data: { device_id }
  })
}

export function getDeviceIds(type) {
  return request({
    url: '/device/deviceIds',
    method: 'get',
    params: { type }
  })
}

export function activeDeviceLicense(device_id) {
  return request({
    url: '/device/active/license',
    method: 'put',
    data: { device_id }
  })
}
