<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.channelId" placeholder="channelId" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.group_id" placeholder="group_id" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="直播状态" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.type" placeholder="类型" clearable style="width: 100px" class="filter-item">
        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="95">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="channelId">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.channelId, $event)">{{ row.channelId }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="groupId" width="95">
        <template slot-scope="{row}">
          {{ row.group_id }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="房主id" width="95">
        <template slot-scope="{row}">
          {{ row.host_uid }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="resource_id">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.resource_id, $event)">点击复制</el-button>
        </template>
      </el-table-column>
      <el-table-column label="sid" width="150" align="center">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.sid, $event)">点击复制</el-button>
        </template>
      </el-table-column>
      <el-table-column label="记录类型" width="110" align="center">
        <template slot-scope="{row}">
          {{ row.type|typeFilter }}
        </template>
      </el-table-column>
      <el-table-column class-name="status-col" label="直播状态" width="110" align="center">
        <template slot-scope="{row}">
          {{ row.status|statusFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" width="200">
        <template slot-scope="{row}">
          <span>{{ row.created_at| formatTime }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="updated_at" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row.file_url_data)">查看更多</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="handleFilter" />
    <el-dialog
      title="展示数据"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-button type="primary" size="mini" @click="onClipboard(jsonData, $event)">点击复制</el-button>
      <div class="editor-container">
        <json-viewer
          :value="jsonData"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList } from '@/api/conference'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'
import JsonViewer from 'vue-json-viewer'
const statusOptions = [
  { label: '已结束', value: 0 },
  { label: '直播中', value: 1 }
]
const typeOptions = [
  { label: '合流', value: 'mix' },
  { label: '分流', value: 'individual' }
]
export default {
  name: 'Group',
  components: { Pagination, JsonViewer },

  filters: {
    statusFilter(status) {
      const statusMap = {
        0: '已结束',
        1: '直播中'
      }
      return statusMap[status]
    },
    typeFilter(type) {
      const typeMap = {
        'mix': '合流录制',
        'individual': '分流录制'
      }
      return typeMap[type]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        subject: null,
        service_type: null,
        type: 'mix'
      },
      dialogVisible: false,
      listLoading: true,
      jsonData: {},
      statusOptions,
      typeOptions
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    showDialog(data) {
      this.dialogVisible = true
      this.jsonData = data
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
