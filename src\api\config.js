import request from '@/utils/request'
export async function getInfo(params) {
  const data = await request({
    url: '/config',
    method: 'get',
    params
  })
  return data.data.data
}

export async function getList(params) {
  const data = await request({
    url: '/config',
    method: 'get',
    params
  })
  return data.data.list
}

export function updateConfig(data) {
  return request({
    url: '/config',
    method: 'put',
    data
  })
}

export function addRegionPermissionConfig(data) {
  return request({
    url: '/config/region',
    method: 'post',
    data
  })
}
