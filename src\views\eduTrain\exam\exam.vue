<template>
  <div class="exam-container">
    <!-- 考试信息头部 -->
    <div class="exam-header fixed-header">
      <div class="exam-info">
        <div class="exam-title">
          <!-- <el-input v-if="examMode === 1" v-model="paperData.title" :placeholder="lang.exam.title" size="medium" /> -->
          <span>{{ paperData.title || lang.exam.title }}</span>
        </div>
      </div>
      <div class="exam-actions">
        <el-button type="danger" size="large" class="exam-button-danger" @click="back()">{{ examMode === 1 ? lang.exam.actions.cancel : lang.exam.actions.back }}</el-button>
        <el-button v-if="examMode === 1" type="success" size="large" class="exam-button-success" @click="saveExam">{{ lang.exam.actions.save }}</el-button>
      </div>
    </div>
    <el-card class="exam-card">
      <!-- 试卷内容 -->
      <div class="exam-content">
        <!-- 简答题部分 -->
        <template v-if="shortAnswerQuestions.length > 0" class="short-answer-questions">
          <div class="question-type-header">
            <h3>{{ lang.exam.questionType.shortAnswer }}</h3>
            <div class="question-type-stats">
              <span>{{ lang.exam.questionCount.replace('{count}', shortAnswerQuestions.length) }}</span>
            </div>
          </div>

          <div class="question-list">
            <div
              v-for="(question, index) in shortAnswerQuestions"
              :key="'sa-' + question.id"
              class="question-item"
            >
              <div class="question-header">
                <div class="question-title">
                  <span class="question-index">{{ index + 1 }}. </span>
                  <span v-if="examMode !== 1">{{ question.title }}</span>
                  <el-input
                    v-else
                    v-model="question.title"
                    size="small"
                    style="width: calc(100% - 25px);"
                    :placeholder="lang.exam.questionPlaceholder"
                  />
                </div>
                <el-popconfirm
                  v-if="examMode === 1"
                  :title="lang.exam.deleteConfirm"
                  @confirm="deleteQuestion('shortAnswer', index)"
                >
                  <el-button slot="reference" type="text" class="delete-btn">
                    <i class="el-icon-delete" />
                  </el-button>
                </el-popconfirm>
              </div>
              <!-- 简答题描述 -->
              <div v-if="question.description && examMode !== 1" class="question-description">
                {{ question.description }}
              </div>
              <div class="question-description" v-if="examMode === 1">
                <el-input
                  v-model="question.description"
                  type="textarea"
                  :rows="2"
                  :placeholder="lang.exam.descriptionPlaceholder"
                />
              </div>
              <!-- 简答题图片管理 -->
              <div class="image-manager-container">
                <div v-if="examMode === 1" class="add-image-button" @click="triggerImageUpload('sa-' + question.id)">
                  <i class="el-icon-plus" />
                </div>
                <div
                  v-for="(image, imgIndex) in getQuestionImages('sa-' + question.id)"
                  :key="question.id + '-img-' + imgIndex"
                  class="thumbnail-item"
                >
                  <!-- 根据文件类型显示不同内容 -->
                  <template v-if="image.fileType === 'video'">
                    <div class="video-thumbnail-placeholder" @click="viewImages('sa-' + question.id, imgIndex)">
                      <i class="el-icon-video-camera video-icon" />
                      <!-- <span class="video-label">视频</span> -->
                    </div>
                  </template>
                  <template v-else>
                    <img :src="image.url" class="thumbnail-image-small" :alt="image.label || ('Image ' + (imgIndex + 1))" @click="viewImages('sa-' + question.id, imgIndex)">
                  </template>
                  <el-button
                    v-if="examMode === 1"
                    type="danger"
                    size="mini"
                    class="delete-image-btn"
                    icon="el-icon-delete"
                    @click.stop="deleteImage('sa-' + question.id, imgIndex)"
                  />
                </div>
              </div>
              <input :ref="'fileInput_sa-' + question.id" type="file" :accept="acceptFileTypes" multiple style="display: none;" @change="handleImageUpload($event, 'sa-' + question.id)">
            </div>
          </div>
        </template>

        <!-- 实操题部分 -->
        <template v-if="operationQuestions.length > 0" class="operation-questions">
          <div class="question-type-header">
            <h3>{{ lang.exam.questionType.operation }}</h3>
            <div class="question-type-stats">
              <span>{{ lang.exam.questionCount.replace('{count}', operationQuestions.length) }}</span>
            </div>
          </div>

          <div class="question-list">
            <div
              v-for="(question, index) in operationQuestions"
              :key="'op-' + question.id"
              class="question-item"
            >
              <div class="question-header">
                <div class="question-title">
                  <span class="question-index">{{ index + 1 }}. </span>
                  <span v-if="examMode !== 1">{{ question.title }}</span>
                  <el-input
                    v-else
                    v-model="question.title"
                    size="small"
                    style="width: calc(100% - 25px);"
                    :placeholder="lang.exam.questionPlaceholder"
                  />
                </div>
                <el-popconfirm
                  v-if="examMode === 1"
                  :title="lang.exam.deleteConfirm"
                  @confirm="deleteQuestion('operation', index)"
                >
                  <el-button slot="reference" type="text" class="delete-btn">
                    <i class="el-icon-delete" />
                  </el-button>
                </el-popconfirm>
              </div>
              <!-- 实操题图片管理 -->
              <div class="image-manager-container">
                <div v-if="examMode === 1" class="add-image-button" @click="triggerImageUpload('op-' + question.id)">
                  <i class="el-icon-plus" />
                </div>
                <div
                  v-for="(image, imgIndex) in getQuestionImages('op-' + question.id)"
                  :key="question.id + '-op-img-' + imgIndex"
                  class="thumbnail-item"
                >
                  <!-- 根据文件类型显示不同内容 -->
                  <template v-if="image.fileType === 'video'">
                    <div class="video-thumbnail-placeholder" @click="viewImages('op-' + question.id, imgIndex)">
                      <i class="el-icon-video-camera video-icon" />
                      <!-- <span class="video-label">视频</span> -->
                    </div>
                  </template>
                  <template v-else>
                    <img :src="image.url" class="thumbnail-image-small" :alt="image.label || ('Image ' + (imgIndex + 1))" @click="viewImages('op-' + question.id, imgIndex)">
                  </template>
                  <el-button
                    v-if="examMode === 1"
                    type="danger"
                    size="mini"
                    class="delete-image-btn"
                    icon="el-icon-delete"
                    @click.stop="deleteImage('op-' + question.id, imgIndex)"
                  />
                </div>
              </div>
              <input :ref="'fileInput_op-' + question.id" type="file" :accept="acceptFileTypes" multiple style="display: none;" @change="handleImageUpload($event, 'op-' + question.id)">

              <!-- 子题目列表 -->
              <div v-if="question.subTopic && question.subTopic.length > 0" class="sub-questions">
                <div
                  v-for="(subQuestion, subIndex) in getValidSubTopics(question.subTopic)"
                  :key="'sub-' + question.id + '-' + subIndex"
                  class="sub-question-item"
                >
                  <!-- 只有当子题目不是NOSHOWSUBTOPICASTOPIC时才显示标题 -->
                  <div v-if="subQuestion.title !== 'NOSHOWSUBTOPICASTOPIC'" class="sub-question-title">
                    <span class="sub-question-index">{{ subIndex + 1 }}. </span>
                    <span v-if="examMode !== 1">{{ subQuestion.title }}</span>
                    <el-input
                      v-else
                      v-model="subQuestion.title"
                      size="small"
                      style="width: calc(100% - 25px);"
                      :placeholder="lang.exam.questionPlaceholder"
                    />
                    <el-popconfirm
                      v-if="examMode === 1"
                      :title="lang.exam.deleteConfirm"
                      @confirm="deleteSubQuestion(index, subIndex)"
                    >
                      <el-button slot="reference" type="text" class="delete-btn">
                        <i class="el-icon-delete" />
                      </el-button>
                    </el-popconfirm>
                  </div>
                  <div v-if="subQuestion.description && examMode !== 1" class="sub-question-description">
                    {{ subQuestion.description }}
                  </div>
                  <div v-if="examMode === 1" class="sub-question-description">
                    <div style="display: flex; align-items: flex-start; gap: 10px;">
                      <el-input
                        v-model="subQuestion.description"
                        type="textarea"
                        :rows="2"
                        :placeholder="lang.exam.descriptionPlaceholder"
                        style="flex: 1;"
                      />
                    </div>
                  </div>
                  <!-- 子题目图片管理 -->
                  <div class="image-manager-container">
                    <div v-if="examMode === 1" class="add-image-button" @click="triggerImageUpload('op-' + question.id + '-sub-' + subIndex)">
                      <i class="el-icon-plus" />
                    </div>
                    <div
                      v-for="(image, imgIndex) in getQuestionImages('op-' + question.id + '-sub-' + subIndex)"
                      :key="question.id + '-sub-' + subIndex + '-img-' + imgIndex"
                      class="thumbnail-item"
                      @click="viewImages('op-' + question.id + '-sub-' + subIndex, imgIndex)"
                    >
                      <!-- 根据文件类型显示不同内容 -->
                      <template v-if="image.fileType === 'video'">
                        <div class="video-thumbnail-placeholder" @click="viewImages('op-' + question.id + '-sub-' + subIndex, imgIndex)">
                          <i class="el-icon-video-camera video-icon" />
                          <!-- <span class="video-label">视频</span> -->
                        </div>
                      </template>
                      <template v-else>
                        <img :src="image.url" class="thumbnail-image-small" :alt="image.label || ('Image ' + (imgIndex + 1))" @click="viewImages('op-' + question.id + '-sub-' + subIndex, imgIndex)">
                      </template>
                      <el-button
                        v-if="examMode === 1"
                        type="danger"
                        size="mini"
                        class="delete-image-btn"
                        icon="el-icon-delete"
                        @click.stop="deleteImage('op-' + question.id + '-sub-' + subIndex, imgIndex)"
                      />
                    </div>
                  </div>
                  <input :ref="'fileInput_op-' + question.id + '-sub-' + subIndex" type="file" :accept="acceptFileTypes" multiple style="display: none;" @change="handleImageUpload($event, 'op-' + question.id + '-sub-' + subIndex)">
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </el-card>
    <base-gallery ref="imageGallery" :loading="galleryLoading" />
  </div>
</template>

<script>
import { zh, en } from '../lang.js'
import { mapState } from 'vuex'
import BaseGallery from './baseGallery.vue'
import { eduService } from '@/api/education.js'
import OSS from 'ali-oss' // 引入Aliyun OSS SDK
import { getOssStsToken } from '@/api/common.js'

export default {
  name: 'ExamView',
  components: { BaseGallery },
  data() {
    return {
      examMode: 0, // 默认为查看模式，目前没用到
      currentTestID: null,
      imageData: {}, // 存储每个问题的图片 { 'questionId': [{ url: '...', fileType: 'image', label: '...' }] }
      galleryLoading: false, // 控制图片库加载状态
      currentLanguage: 'zh', // 新增当前语言变量
      allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'tif', 'ico', 'heic', 'dcm'],
      allowedVideoTypes: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp', 'mpeg', 'mpg', 'm4v'],
      maxFileSize: 20 * 1024 * 1024,
      uploadingFiles: new Set(), // 跟踪正在上传的文件
      ossClient: null
    }
  },
  computed: {
    lang() {
      return this.currentLanguage === 'zh' ? zh : en
    },

    ...mapState('homework', {
      rawUploadedPaper: state => state.uploadedPaper
    }),
    paperData() {
      if (this.rawUploadedPaper && this.rawUploadedPaper.paperInfo) {
        const info = this.rawUploadedPaper.paperInfo
        return {
          title: info.title,
          content: info.content || [],
          questionCount: info.questionCount || 0
        }
      }
      return { // 返回默认结构以避免模板错误
        title: '',
        content: [],
        questionCount: 0
      }
    },
    acceptFileTypes() {
      const imageTypes = this.allowedImageTypes.join(',.').toLowerCase()
      const videoTypes = this.allowedVideoTypes.join(',.').toLowerCase()
      return `.${imageTypes},.${videoTypes}`
    },
    shortAnswerQuestions() {
      if (!this.paperData || !this.paperData.content) {
        return []
      }
      const shortAnswerSection = this.paperData.content.find(section => section.type === 'shortAnswer')
      return shortAnswerSection ? shortAnswerSection.list || [] : []
    },
    operationQuestions() {
      if (!this.paperData || !this.paperData.content) {
        return []
      }
      const operationSection = this.paperData.content.find(section => section.type === 'operation')
      return operationSection ? operationSection.list || [] : []
    }
  },

  mounted() {
    // 检查是否有试卷数据，如果没有则返回到教培项目页面
    if (!this.rawUploadedPaper || !this.rawUploadedPaper.paperInfo) {
      this.$router.replace('/eduTrain')
      return
    }

    if (this.$route.params.mode) {
      this.examMode = parseInt(this.$route.params.mode, 10)
    }

    // 检查URL中是否有语言参数
    if (this.$route.query.lang) {
      this.currentLanguage = this.$route.query.lang
    }
    // 从路由参数中获取 testID
    if (this.$route.query.testID) {
      this.currentTestID = this.$route.query.testID
    }

    // 加载已有的图片数据
    this.loadExistingImages()
  },
  methods: {
    back(length) {
      if (typeof length === 'number') {
        this.$router.go(length * -1)
      } else {
        this.$router.back()
      }
      return this
    },
    getValidSubTopics(subTopics) {
      if (!subTopics) return []
      // 如果只有一道子题目且标题为 NOSHOWSUBTOPICASTOPIC，则保留它
      if (subTopics.length === 1 && subTopics[0].title === 'NOSHOWSUBTOPICASTOPIC') {
        return subTopics
      }

      // 否则过滤掉标题为 NOSHOWSUBTOPICASTOPIC 的子题目
      return subTopics.filter(sub => sub.title !== 'NOSHOWSUBTOPICASTOPIC')
    },
    // 判断是否为特殊的单子题目情况（只有描述，不显示标题）
    isSpecialSingleSubTopic(question) {
      return question.subTopic &&
             question.subTopic.length === 1 &&
             question.subTopic[0].title === 'NOSHOWSUBTOPICASTOPIC'
    },
    async saveExam() {
      // 确保 examMode 为 1 (编辑模式)
      if (this.examMode !== 1) {
        this.$message.warn(this.lang.exam.messages.notInEditMode)
        return
      }

      // 从 this.currentTestID 获取 testID，如果不存在则回退到 this.rawUploadedPaper.testID
      const testIDToUse = this.currentTestID || (this.rawUploadedPaper ? this.rawUploadedPaper.testID : null)

      // if (!testIDToUse) {
      //   this.$message.error(this.lang.exam.messages.missingTestID || 'Missing Test ID, cannot save.')
      //   console.error('Error: Missing testID in saveExam')
      //   return
      // }

      // 更新总题数
      this.updateQuestionCount()

      const paperInfoForPayload = {
        questionCount: this.paperData.questionCount,
        content: this.paperData.content.map(section => ({
          type: section.type,
          count: section.list ? section.list.length : 0,
          list: section.list.map((question) => {
            let imageListKey
            if (section.type === 'shortAnswer') {
              imageListKey = `sa-${question.id}`
            } else if (section.type === 'operation') {
              imageListKey = `op-${question.id}`
            }

            const processedQuestion = {
              ...question,
              imageList: imageListKey ? (this.getQuestionImages(imageListKey) || []) : (question.imageList || [])
            }

            if (section.type === 'operation' && processedQuestion.subTopic && Array.isArray(processedQuestion.subTopic)) {
              processedQuestion.subTopic = processedQuestion.subTopic.map((sub, subIndex) => {
                const subImageListKey = `op-${question.id}-sub-${subIndex}`
                return {
                  ...sub,
                  imageList: this.getQuestionImages(subImageListKey) || []
                }
              })
            }
            return processedQuestion
          })
        }))
      }

      const payload = {
        testID: testIDToUse,
        paperInfo: paperInfoForPayload,
        questionCount: this.paperData.questionCount // Top-level question count
      }

      try {
        const response = await eduService.updateAssessPaper(payload)
        if (response && response.code === 200) {
          this.$message.success(this.lang.messages.updateSuccess)
          this.back()
        } else {
          this.$message.error(response.message || this.lang.exam.messages.updateFailed)
        }
      } catch (error) {
        this.$message.error(this.lang.exam.messages.updateFailed)
      }
    },
    deleteQuestion(type, index) {
      // 只在编辑模式下允许删除
      if (this.examMode !== 1) return

      // 查找对应类型的问题列表
      const contentIndex = this.paperData.content.findIndex(section => section.type === type)
      if (contentIndex === -1) return
      // 删除问题
      this.paperData.content[contentIndex].list.splice(index, 1)
      // 更新题目总数
      this.updateQuestionCount()
      this.$message.success(this.lang.exam.messages.deleteSuccess)
    },
    deleteSubQuestion(operationIndex, subIndex) {
      // 只在编辑模式下允许删除
      if (this.examMode !== 1) return

      // 找到实操题的内容
      const operationContent = this.paperData.content.find(section => section.type === 'operation')
      if (!operationContent || !operationContent.list[operationIndex]) return

      // 删除子题目
      if (operationContent.list[operationIndex].subTopic && operationContent.list[operationIndex].subTopic.length > subIndex) {
        operationContent.list[operationIndex].subTopic.splice(subIndex, 1)
        this.$message.success(this.lang.exam.messages.deleteSuccess)
      }
    },
    updateQuestionCount() {
      // 计算所有题目的数量
      let totalCount = 0
      this.paperData.content.forEach(section => {
        if (section.list && Array.isArray(section.list)) {
          totalCount += section.list.length
        }
      })
      this.paperData.questionCount = totalCount
    },
    // --- 图片上传和查看相关方法 ---
    getQuestionImages(questionId) {
      return this.imageData[questionId] || []
    },

    triggerImageUpload(questionId) {
      // 只在编辑模式下允许上传图片
      if (this.examMode !== 1) return

      const inputRefName = `fileInput_${questionId}`
      // 由于 vue-loader 优化，v-for 中的 ref 会是一个数组
      // 但这里我们给每个 input 设置了动态的 :ref 字符串，所以它应该直接是元素
      const inputElement = this.$refs[inputRefName]
      if (inputElement && typeof inputElement.click === 'function') {
        inputElement.click()
      } else if (Array.isArray(inputElement) && inputElement[0] && typeof inputElement[0].click === 'function') {
        // 以防万一，如果它确实是个数组（比如在非常老的Vue版本或特殊配置下）
        inputElement[0].click()
      } else {
        console.error('Could not find input element to click:', inputRefName, this.$refs)
      }
    },

    async handleImageUpload(event, questionId) {
      // 只在编辑模式下允许上传图片
      if (this.examMode !== 1) return

      const files = event.target.files
      if (!files || files.length === 0) {
        return
      }

      this.galleryLoading = true // 开始加载

      let processedFilesCount = 0
      const totalFiles = files.length
      const newImages = []

      // 定义检查函数，移到循环前面
      const checkAllFilesProcessed = () => {
        if (processedFilesCount === totalFiles) {
          if (newImages.length > 0) {
            if (!this.imageData[questionId]) {
              this.$set(this.imageData, questionId, [])
            }
            this.imageData[questionId].push(...newImages)
            this.$message.success(`${newImages.length} ${this.lang.exam.messages.uploadSuccess}`)
          }
          this.galleryLoading = false // 所有文件处理完毕，结束加载
        }
      }

      for (const file of Array.from(files)) {
        const validationResult = this.validateImageFile(file)
        if (validationResult.valid) {
          try {
            const ossUrl = await this.uploadToOSS(file, questionId)
            if (ossUrl) {
              // 根据文件扩展名确定文件类型
              const fileExt = file.name.split('.').pop().toLowerCase()
              const fileType = this.allowedVideoTypes.includes(fileExt) ? 'video' : 'image'
              newImages.push({
                url: ossUrl,
                fileType: fileType,
                label: file.name
              })
            }
          } catch (error) {
            console.error(`OSS ${this.lang.exam.messages.uploadFailed}:`, error)
            this.$message.error(`${file.name} ${this.lang.exam.messages.uploadFailed}`)
          }
        } else {
          this.$message.error(validationResult.message)
        }
        processedFilesCount++
        checkAllFilesProcessed()
      }

      // 清空input的值，以便可以再次选择相同的文件
      event.target.value = null
    },

    validateImageFile(file) {
      // 使用data中定义的最大文件大小
      const fileExt = file.name.split('.').pop().toLowerCase()
      const maxFileSize = this.maxFileSize
      const allAllowedTypes = [...this.allowedImageTypes, ...this.allowedVideoTypes]
      // 检查文件类型
      if (!allAllowedTypes.includes(fileExt)) {
        return {
          valid: false,
          message: `${file.name}: ${this.lang.exam.messages.invalidImageFormat}`
        }
      }
      // 检查文件大小
      if (file.size > maxFileSize) {
        return {
          valid: false,
          message: `${file.name}: ${this.lang.exam.messages.imageTooLarge}`
        }
      }

      return {
        valid: true,
        message: ''
      }
    },

    // 上传文件到OSS
    async uploadToOSS(file, questionId) {
      try {
        // 生成唯一的文件名
        const timestamp = Date.now()
        const randomStr = Math.random().toString(36).substring(2, 15)
        const fileExt = file.name.split('.').pop()
        const fileName = `/eduTrain/exam/${questionId}/${timestamp}_${randomStr}.${fileExt}`

        // 获取OSS临时凭证
        const res = await getOssStsToken(fileName)
        if (!res || !res.data) {
          throw new Error(this.lang.exam.messages.ossCredentialsFailed)
        }

        // 创建OSS客户端
        this.ossClient = new OSS({
          endpoint: res.data.endpoint,
          accessKeyId: res.data.accessKeyId,
          accessKeySecret: res.data.accessKeySecret,
          bucket: res.data.bucket,
          stsToken: res.data.stsToken,
          xhrTimeout: 60000
        })

        // 上传文件
        const result = await this.ossClient.put(fileName, file)

        if (result && result.url) {
          return result.url
        } else {
          throw new Error(this.lang.exam.messages.uploadFailed)
        }
      } catch (error) {
        console.error(`OSS ${this.lang.exam.messages.uploadFailed}:`, error)
        throw error
      }
    },

    viewImages(questionId, startIndex = 0) {
      const images = this.getQuestionImages(questionId)
      this.$refs.imageGallery.openGallery(images, startIndex)
    },

    // 加载已有的图片数据
    loadExistingImages() {
      if (!this.paperData || !this.paperData.content) {
        return
      }

      this.paperData.content.forEach(section => {
        if (!section.list) return

        section.list.forEach(question => {
          // 处理简答题图片
          if (section.type === 'shortAnswer') {
            const questionKey = `sa-${question.id}`
            if (question.imageList && question.imageList.length > 0) {
              this.$set(this.imageData, questionKey, [...question.imageList])
            }
          }

          // 处理实操题图片
          if (section.type === 'operation') {
            const questionKey = `op-${question.id}`
            if (question.imageList && question.imageList.length > 0) {
              this.$set(this.imageData, questionKey, [...question.imageList])
            }

            // 处理实操题的子题目图片
            if (question.subTopic && Array.isArray(question.subTopic)) {
              question.subTopic.forEach((subQuestion, subIndex) => {
                const subQuestionKey = `op-${question.id}-sub-${subIndex}`
                if (subQuestion.imageList && subQuestion.imageList.length > 0) {
                  this.$set(this.imageData, subQuestionKey, [...subQuestion.imageList])
                }
              })
            }
          }
        })
      })
    },

    // 删除图片
    deleteImage(questionId, imageIndex) {
      if (!this.imageData[questionId] || this.examMode !== 1) {
        return
      }
      this.imageData[questionId].splice(imageIndex, 1)
      this.$message.success(this.lang.exam.messages.deleteSuccess)
      // this.$confirm(this.lang.exam.messages.deleteImageConfirm, this.lang.exam.messages.confirmTitle, {
      //   confirmButtonText: this.lang.buttons.confirm,
      //   cancelButtonText: this.lang.buttons.cancel,
      //   type: 'warning'
      // }).then(() => {
      //   this.imageData[questionId].splice(imageIndex, 1)
      //   this.$message.success(this.lang.exam.messages.deleteSuccess)
      // }).catch(() => {
      //   // 用户取消删除
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.exam-container {
  width: 100%;
  padding: 20px;
}

.exam-button-danger {
//   background-color: #ff675c;
//   border-color: #ff675c;
//   color: #fff;
  width: 100px;
}

.exam-button-success {
  background-color: #00c59d;
  border-color: #00c59d;
  color: #fff;
  width: 100px;
}

.exam-card {
  margin-bottom: 20px;
  margin-top: 70px;
}

.fixed-header {
  position: fixed;
  top: 50px;
  right: 0;
  z-index: 9;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
  width: calc(100% - 210px);
  box-sizing: border-box;
  transition: width 0.28s;
}

/* 当侧边栏收起时，宽度自适应 */
.hideSidebar .fixed-header {
  width: calc(100% - 54px)
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 10px;
  // padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.exam-title {
  font-size: 21px;
  font-weight: bold;
  // margin-bottom: 8px;
}
.exam-content {
    background-color: #ebeff2;
    padding: 10px 20px;
    border-radius: 6px;
}

.question-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.question-type-header h3 {
  margin: 0;
  color: #303133;
}

.question-type-stats {
  display: flex;
  gap: 15px;
  color: #666;
}

.question-list {
  margin-bottom: 30px;
}

.question-item {
  margin-bottom: 25px;
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px; /* 给标题和按钮之间留出一些空间 */
}

.question-title {
  width: 100%;
  // display: flex;
  align-items: center;
  font-weight: bold;
}

.question-index {
  margin-right: 5px;
}

.question-description {
  margin-left: 20px;
  color: #606266;
  white-space: pre-wrap;
  margin-bottom: 15px;
}

.question-actions-image {
  margin-top: 8px; /* 按钮组与上方内容的间距 */
  margin-bottom: 8px; /* 按钮组与下方子题目或下一个题目的间距 */
}

.sub-questions {
  margin-top: 15px;
  margin-left: 15px;
}

.sub-question-item {
  margin-bottom: 15px;
}

.sub-question-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 8px;
}

.sub-question-index {
  margin-right: 5px;
}

.sub-question-description {
  margin-left: 20px;
  color: #606266;
  white-space: pre-wrap;
  margin-bottom: 8px; /* 描述与下方按钮的间距 */
}

.thumbnail-upload-area {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    border-color: #409eff;
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .thumbnail-placeholder {
    font-size: 24px;
    color: #909399;
  }
}

.image-manager-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.thumbnail-item {
  width: 100px;
  height: 100px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;

  &:hover {
    border-color: #409eff;
  }

  .thumbnail-image-small {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .video-thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;

    .video-icon {
      font-size: 28px;
      margin-bottom: 4px;
    }

    // .video-label {
    //   font-size: 12px;
    //   font-weight: 500;
    // }

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.delete-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  padding: 4px;
  border-radius: 50%;
  z-index: 10;
}

.add-image-button {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  margin-left: 20px;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    border-color: #409eff;
  }

  .el-icon-plus {
    font-size: 24px;
    color: #909399;
  }
}

  .delete-btn {
  margin-left: 10px;
  color: #F56C6C;

  &:hover {
    color: #ff5a5a;
  }

  .el-icon-delete {
    font-size: 16px;
  }
}

</style>
