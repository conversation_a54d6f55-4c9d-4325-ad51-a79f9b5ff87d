import request from '@/utils/request'

export function getOrgsUser(params) {
  return request({
    url: '/statistic/user/list',
    method: 'get',
    params
  })
}

export function getStaticData(params) {
  const { timeScope, userIds } = params
  return request({
    url: '/statistic/data',
    method: 'get',
    params: { userIds, timeScope }
  })
}

export function getChartData(params) {
  const { timeScope, granularity, statisItem, userIds } = params
  return request({
    url: '/statistic/chart',
    method: 'post',
    data: { userIds, timeScope, granularity, statisItem }
  })
}

export function liveStatis() {
  return request({
    url: '/statistic/live/click',
    method: 'get'
  })
}
