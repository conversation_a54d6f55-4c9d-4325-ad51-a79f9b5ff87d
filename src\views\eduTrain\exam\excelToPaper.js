const xlsx = require('xlsx')

class ExcelParser {
  constructor() {
    this.currentIndex = 0
    this.totalCount = 0
    this.content = []
    this.contentTypeSheets = {}
  }

  cleanText(text) {
    if (typeof text !== 'string') {
      return ''
    }
    return text.trim().replace(/[\r\n\t\f\v ]+/g, ' ')
  }

  // 安全获取数据
  safeGetValue(row, key, defaultValue = '') {
    const value = row[key]
    return value !== undefined && value !== null ? value : defaultValue
  }

  // 验证必填字段
  validateRequiredField(value, fieldName) {
    if (value === undefined || value === null || value === '') {
      throw new Error(`FIELD_REQUIRED:${fieldName}`)
    }
    return value
  }

  processShortAnswerSheet() {
    let totalCount = 0
    const sheet = this.contentTypeSheets.shortAnswer
    if (!sheet) {
      return
    }

    // 获取表头
    const headers = this.getSheetHeaders(sheet)
    if (!headers || headers.length < 3) {
      return
    }

    const titleKey = headers[0]
    const answerKey = headers[2]

    const sheetData = xlsx.utils.sheet_to_json(sheet)
    if (sheetData.length === 0) {
      return
    }

    const list = []
    sheetData.forEach((row) => {
    // 获取并验证题目
      const title = this.validateRequiredField(
        this.safeGetValue(row, titleKey),
        '题目'
      )

      // 获取答案
      const answer = this.safeGetValue(row, answerKey)

      this.currentIndex += 1
      totalCount += 1

      list.push({
        id: Math.floor(Math.random() * Number.MAX_SAFE_INTEGER),
        index: this.currentIndex,
        title,
        type: 'shortAnswer',
        imageList: [],
        value: '',
        subTopic: [],
        answer
      })
    })

    if (list.length > 0) {
      this.content.push({
        type: 'shortAnswer',
        count: totalCount,
        list
      })
      this.totalCount += totalCount
    }
  }

  processOperationSheet() {
    let totalCount = 0
    const sheet = this.contentTypeSheets.operation
    if (!sheet) {
      return
    }

    const sheetData = xlsx.utils.sheet_to_json(sheet)
    if (sheetData.length === 0) {
      return
    }

    const list = []
    let currentItem = null

    // 定义列名
    const titleKey = '*题目'
    const subTitleKey = '子题目'
    const descriptionKey = '详细要求'

    sheetData.forEach((row) => {
      // 清理行数据
      const cleanRow = {}
      Object.keys(row).forEach((key) => {
        cleanRow[key] = this.cleanText(this.safeGetValue(row, key))
      })

      // 处理主题目
      if (cleanRow[titleKey]) {
        // 验证题目
        this.validateRequiredField(cleanRow[titleKey], '题目')

        this.currentIndex += 1
        this.totalCount += 1
        totalCount += 1

        currentItem = {
          id: Math.floor(Math.random() * Number.MAX_SAFE_INTEGER),
          index: this.currentIndex,
          title: cleanRow[titleKey],
          type: 'operation',
          description: '',
          imageList: [],
          value: [],
          subTopic: [
            {
              title: cleanRow[subTitleKey]
                ? cleanRow[subTitleKey]
                : 'NOSHOWSUBTOPICASTOPIC',
              description: this.cleanText(cleanRow[descriptionKey] || ''),
              value: [],
              imageList: []
            }
          ]
        }
        list.push(currentItem)
      } else if (currentItem && cleanRow[subTitleKey]) {
        // 验证子题目
        this.validateRequiredField(cleanRow[subTitleKey], '子题目')

        // 处理子题目
        const subData = {
          title: cleanRow[subTitleKey] || '',
          description: this.cleanText(cleanRow[descriptionKey] || ''),
          value: [],
          imageList: []
        }
        currentItem.subTopic.push(subData)
      }
    })

    if (list.length > 0) {
      this.content.push({
        type: 'operation',
        count: totalCount,
        list
      })
    }
  }

  // 获取表格的表头
  getSheetHeaders(sheet) {
    const range = xlsx.utils.decode_range(sheet['!ref'] || 'A1')
    const headers = []

    // 只读取第一行作为表头
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = xlsx.utils.encode_cell({ r: range.s.r, c: C })
      const cell = sheet[cellAddress]

      if (cell && cell.v) {
        headers.push(cell.v)
      } else {
        headers.push(`Column${C}`) // 为空的列添加默认名称
      }
    }

    return headers
  }

  async parse(file) {
    try {
      const data = await file.arrayBuffer()
      const workbook = xlsx.read(data, { type: 'array' })

      // 行列数验证
      for (const sheetName of workbook.SheetNames) {
        const sheet = workbook.Sheets[sheetName]
        const range = xlsx.utils.decode_range(sheet['!ref'] || 'A1')
        const rowCount = range.e.r - range.s.r + 1
        const colCount = range.e.c - range.s.c + 1

        if (rowCount > 10000) {
          throw new Error(`MAX_ROWS:${sheetName}`)
        }
        if (colCount > 20) {
          throw new Error(`MAX_COLUMNS:${sheetName}`)
        }
      }

      // 重置解析器状态
      this.currentIndex = 0
      this.totalCount = 0
      this.content = []
      this.contentTypeSheets = {}

      // 识别各类型的表格
      workbook.SheetNames.forEach((sheetName) => {
        if (sheetName.includes('简答')) {
          this.contentTypeSheets.shortAnswer = workbook.Sheets[sheetName]
        } else if (sheetName.includes('实操')) {
          this.contentTypeSheets.operation = workbook.Sheets[sheetName]
        }
      })

      // 处理各类型的表格
      this.processShortAnswerSheet()
      this.processOperationSheet()

      return {
        paperInfo: {
          title: file.name.replace(/\.[^/.]+$/, ''),
          questionCount: this.totalCount,
          content: this.content,
          contentType: 2,
          author: ''
        }
      }
    } catch (error) {
      console.error('Excel解析错误:', error)
      throw error
    }
  }
}

export const convertExcelToMongoContent = async(file) => {
  const parser = new ExcelParser()
  return await parser.parse(file)
}
