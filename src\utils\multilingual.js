/**
 * <PERSON> 2023.7.26
 */

/**
 * 检查数组是否没有元素
 * @param {Array} arr 数组
 * @returns 布尔值
 */
function isNoElement(arr) {
  if (arr instanceof Array) {
    if (arr.length === 0 || null) {
      return true
    }
  }
  if (arr == null || arr === undefined || arr === '') {
    return true
  }
  return false
}

/**
 * 将SQL表中的KeyData与VUE前端业务逻辑对应，JSON化
 * @param {*} val 数组
 * @returns 结果
 */
export function keyDataJSONize(val) {
  const data = val
  try {
    if (null || isNoElement(val.key_android)) {
      data.key_android = null
    } else {
      data.key_android = JSON.stringify(val.key_android)
    }
  } catch (e) {
    if (typeof val.key_android === 'string') {
      data.key_android = val.key_android
    }
  }
  try {
    if (null || isNoElement(val.key_ios)) {
      data.key_ios = null
    } else {
      data.key_ios = JSON.stringify(val.key_ios)
    }
  } catch (e) {
    if (typeof val.key_ios === 'string') {
      data.key_ios = val.key_ios
    }
  }
  try {
    if (null || isNoElement(val.key_pc)) {
      data.key_pc = null
    } else {
      data.key_pc = JSON.stringify(val.key_pc)
    }
  } catch (e) {
    if (typeof val.key_pc === 'string') {
      data.key_pc = val.key_pc
    }
  }
  try {
    if (null || isNoElement(val.key_web)) {
      data.key_web = null
    } else {
      data.key_web = JSON.stringify(val.key_web)
    }
  } catch (e) {
    if (typeof val.key_web === 'string') {
      data.key_web = val.key_web
    }
  }
  return data
}
