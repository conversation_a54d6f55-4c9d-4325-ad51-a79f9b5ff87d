import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: 'Dashboard', icon: 'dashboard' }
    }]
  }
]
const ROLES = {
  'ADMIN': 2,
  'SUPER_ADMIN': 3,
  'DIRECTOR': 4
}
export const asyncRoutes = [
  {
    path: '/user',
    component: Layout,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '用户',
        component: () => import('@/views/user/index'),
        meta: { title: '用户', icon: 'peoples' }
      }
    ]
  },

  {
    path: '/group',
    component: Layout,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '会话',
        component: () => import('@/views/group/index'),
        meta: { title: '会话', icon: 'el-icon-chat-dot-round' }
      }
    ]
  },

  {
    path: '/groupset',
    component: Layout,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '群落',
        component: () => import('@/views/groupset/index'),
        meta: { title: '群落', icon: 'el-icon-chat-dot-square' }
      }
    ]
  },

  {
    path: '/eduTrain',
    component: Layout,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: navigator.language.includes('zh') ? '教培项目' : 'Education & Training',
        component: () => import('@/views/eduTrain/index'),
        meta: { title: navigator.language.includes('zh') ? '教培项目' : 'Education & Training', icon: 'el-icon-collection' }
      },
      {
        path: 'exam/:mode',
        name: 'EduTrainExam',
        component: () => import('@/views/eduTrain/exam/exam'),
        meta: { title: navigator.language.includes('zh') ? '试卷预览' : 'Exam Preview' },
        hidden: true
      }
    ]
  },

  {
    path: '/channel',
    component: Layout,
    meta: { roles: [ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '直播间列表',
        component: () => import('@/views/channel/index'),
        meta: { title: '直播间列表', icon: 'table' }
      }
    ]
  },
  {
    path: '/doppler',
    component: Layout,
    meta: { roles: [ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '多普勒升级配置',
        component: () => import('@/views/doppler/upgrade'),
        meta: { title: '多普勒升级配置', icon: 'table' }
      }
    ]
  },
  // {
  //   path: '/tvwall',
  //   component: Layout,
  //   meta: { roles: [ROLES.SUPER_ADMIN] },
  //   children: [
  //     {
  //       path: '',
  //       name: '主播列表',
  //       component: () => import('@/views/tvwall/index'),
  //       meta: { title: '电视墙列表', icon: 'table' }
  //     }
  //   ]
  // },

  {
    path: '/agora',
    component: Layout,
    meta: { roles: [ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '在线直播',
        component: () => import('@/views/agora/index'),
        meta: { title: '在线直播', icon: 'el-icon-service' }
      }
    ]
  },
  {
    path: '/device',
    component: Layout,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '设备管理',
        component: () => import('@/views/device/index'),
        meta: { title: '设备管理', icon: 'el-icon-mobile-phone' }
      }
    ]
  },
  {
    path: '/statis',
    component: Layout,
    hidden: true,
    meta: { roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '统计',
        component: () => import('@/views/statistical'),
        meta: { title: '统计', icon: 'tab' }
      }
    ]
  },
  {
    path: '/config',
    component: Layout,
    meta: { roles: [ROLES.SUPER_ADMIN] },
    children: [
      {
        path: '',
        name: '配置',
        component: () => import('@/views/configCenter'),
        meta: { title: '配置', icon: 'edit' }
      }
    ]
  },

  // David Zhou 2023.7.25
  {
    path: '/multilingual',
    redirect: '/404',
    component: Layout,
    meta: { roles: [ROLES.SUPER_ADMIN] },
    onlyShowIn: ['dev', 'localhost'],
    children: [
      {
        path: '',
        name: '多语言文本表',
        component: () => import('@/views/multilingual'),
        meta: { title: '多语言文本表', icon: 'el-icon-s-management' }
      }
    ]
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

export const notFoundRoute = {
  path: '*', redirect: '/404', hidden: true // 404 page must be placed at the end !!!
}

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
