<template>
  <div class="dashboard-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="运营配置" name="env">
        <el-form ref="form" :model="envConfig" label-width="150px" :inline="true" size="normal">
          <el-form-item label="反向控制开启">
            <el-switch v-model="envConfig.remoteControlSwitch" :active-value="true" :inactive-value="false" />
          </el-form-item>
          <el-form-item label="App 公告栏">
            <el-switch v-model="envConfig.announcement.switch" :active-value="true" :inactive-value="false" />
            <el-input v-model="envConfig.announcement.content" type="textarea" size="normal" clearable />
          </el-form-item>
          <el-form-item label="隐私协议版本号">
            <el-input v-model="envConfig.privacy_agreement_version" />
          </el-form-item>
          <el-form-item label="灵珂黑名单">
            <el-input v-model="envConfig.blacklist" type="textarea" size="normal" clearable />
          </el-form-item>
          <el-form-item style="display:block;">
            <el-button type="primary" @click="handleUpdate('env', envConfig)">确定</el-button>
            <el-button>取消</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="版本号配置" name="version">
        <h2>版本信息配置</h2>
        <el-form ref="form" :model="versionConfig" label-width="150px" :inline="false" size="normal">
          <el-divider />
          <el-row>
            <el-col :span="6">
              <el-tooltip content="PC自动升级下载使用" placement="top" effect="dark">
                <el-form-item label="PC当前版本号">
                  <el-input v-model="versionConfig.pc.current" />
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.pc.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="0">
              <el-form-item label="上传包"><UploadToOss :obj-key="'pc'" :download-url="versionConfig.pc.downloadUrl" @onUpload="onUpload" /></el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-tooltip content="主页PC下载使用" placement="top" effect="dark">
                <el-form-item label="PC防盗链-当前版本号">
                  <el-input v-model="versionConfig.pc_anti_theft.current" />
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.pc_anti_theft.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="0">
              <el-form-item label="上传包"><UploadToOss :obj-key="'pc_anti_theft'" :download-url="versionConfig.pc_anti_theft.downloadUrl" @onUpload="onUpload" /></el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-tooltip content="主页下载与自动升级使用" placement="top" effect="dark">
                <el-form-item label="Android当前版本号">
                  <el-input v-model="versionConfig.android.current" />
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.android.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="0">
              <el-form-item label="上传包"><UploadToOss :obj-key="'android'" :download-url="versionConfig.android.downloadUrl" @onUpload="onUpload" /></el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-tooltip content="谷歌市场使用" placement="top" effect="dark">
                <el-form-item label="AndroidGooglePlay当前版本号">
                  <el-input v-model="versionConfig.androidGooglePlay.current" />
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.androidGooglePlay.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="上传包"><UploadToOss :obj-key="'androidGooglePlay'" :download-url="versionConfig.androidGooglePlay.downloadUrl" @onUpload="onUpload" /></el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-form-item label="灵珂当前版本号">
                <el-input v-model="versionConfig.ulinker.current" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.ulinker.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="上传包"><UploadToOss :obj-key="'ulinker'" :download-url="versionConfig.ulinker.downloadUrl" @onUpload="onUpload" /></el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-form-item label="灵珂系统当前版本号">
                <el-input v-model="versionConfig.ulinkerSystem.current" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.ulinkerSystem.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="文件大小">
                <el-input v-model="versionConfig.ulinkerSystem.fileSize" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="标题">
                <el-input v-model="versionConfig.ulinkerSystem.title" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="18">
              <el-form-item label="新特性">
                <el-input v-model="versionConfig.ulinkerSystem.newFeature" type="textarea" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="推送时间">
                <el-date-picker
                  v-model="versionConfig.ulinkerSystem.publishTime"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16">
              <el-form-item label="下载地址">
                <el-input v-model="versionConfig.ulinkerSystem.downloadUrl" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="MD5">
                <el-input v-model="versionConfig.ulinkerSystem.md5" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-form-item label="teair系统当前版本号">
                <el-input v-model="versionConfig.teair.current" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.teair.min" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="文件大小">
                <el-input v-model="versionConfig.teair.fileSize" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item label="下载地址">
                <el-input v-model="versionConfig.teair.downloadUrl" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="MD5">
                <el-input v-model="versionConfig.teair.md5" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-row>
            <el-col :span="6">
              <el-form-item label="iOS当前版本号">
                <el-input v-model="versionConfig.ios.current" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最低版本号">
                <el-input v-model="versionConfig.ios.min" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="下载地址">
                <el-input v-model="versionConfig.ios.downloadUrl" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />

          <el-form-item style="float: right;">
            <el-button type="primary" @click="handleUpdate('version', versionConfig)">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="区域功能配置" name="regionPermission">
        <el-button type="primary" size="default" style="float: right;margin-bottom: 10px;" @click="handleAddRegionPermission">新增配置</el-button>

        <el-table
          v-loading="listLoading"
          :data="regionPermissionConfig"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
        >
          <el-table-column align="center" label="国家或地区" width="200">
            <template slot-scope="{row}">
              {{ row.code|regionFilter }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="禁用功能">
            <template slot-scope="{row}">
              {{ row.forbidModule|forbidModuleFilter }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updated_at" label="更多" width="200">
            <template slot-scope="{row}">
              <div class="table_more">
                <el-button type="primary" size="mini" @click="handleUpdateRegionPermission(row)">修改</el-button>
                <el-button type="danger" size="mini" @click="handleDeleteRegionPermission(row.code)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          :title="dialogTitle[dialogStatus]"
          :visible.sync="dialogVisible"
          width="70%"
        >
          <el-form ref="regionForm" :model="regionForm" label-width="150px">
            <el-form-item label="国家或地区">
              <el-select v-if="regionForm.code !=='default'" v-model="regionForm.code" filterable placeholder="请选择">
                <el-option
                  v-for="item in countryDetailMap"
                  :key="item.code"
                  :label="item.zhName"
                  :value="item.code"
                  :disabled="countryDisabled(item.code)"
                />
              </el-select>
              <span v-else style="font-size: 14px;color: #606266;font-weight: 700;">默认</span>
            </el-form-item>
            <el-form-item label="禁用功能">
              <el-checkbox-group v-model="regionForm.forbidModule">
                <el-checkbox v-for="item in functionList" :key="item.value" :label="item.value" :value="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <div class="dialog-footer">
              <el-button @click="dialogVisible= false">取消</el-button>
              <el-button v-if="dialogStatus === 'create'" type="primary" @click="createRegionPermission">确定</el-button>
              <el-button v-else type="primary" @click="updateRegionPermission">确定</el-button>
            </div>
          </el-form>
        </el-dialog>
      </el-tab-pane>

      <el-tab-pane label="定向推送配置" name="targeted">
        <el-form ref="form" :model="targetedConfig" label-width="150px" :inline="false" size="normal">
          <div v-for="(ulinker, index) in targetedConfig.ulinker" :key="`ulinker${index}`">
            <div v-if="index===0">
              <h2 class="targetedTitle">灵珂App</h2>
              <el-button type="primary" size="mini" @click="targetedConfig.ulinker.push({ deviceIds: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' })">添加</el-button>
              <el-button type="danger" size="mini" @click="deleteConfig(targetedConfig.ulinker)">删除</el-button>
            </div>

            <el-row>
              <el-col :span="6">
                <el-form-item label="设备唯一编码">
                  <el-select
                    v-model="ulinker.deviceIds"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                  >
                    <el-option
                      v-for="item of ulinker.deviceIds"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="灵珂App当前版本号">
                  <el-input v-model="ulinker.current" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最低版本号">
                  <el-input v-model="ulinker.min" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="文件大小">
                  <el-input v-model="ulinker.fileSize" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="下载地址">
                  <el-input v-model="ulinker.downloadUrl" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MD5">
                  <el-input v-model="ulinker.md5" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <div v-for="(ulinkerSystem, index) in targetedConfig.ulinkerSystem" :key="`ulinkerSystem${index}`">
            <div v-if="index===0">
              <h2 class="targetedTitle">灵珂系统</h2>
              <el-button type="primary" size="mini" @click="targetedConfig.ulinkerSystem.push({ deviceIds: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' })">添加</el-button>
              <el-button type="danger" size="mini" @click="deleteConfig(targetedConfig.ulinkerSystem)">删除</el-button>
            </div>

            <el-row>
              <el-col :span="6">
                <el-form-item label="设备唯一编码">
                  <el-select
                    v-model="ulinkerSystem.deviceIds"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                  >
                    <el-option
                      v-for="item of ulinkerSystem.deviceIds"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="灵珂系统当前版本号">
                  <el-input v-model="ulinkerSystem.current" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最低版本号">
                  <el-input v-model="ulinkerSystem.min" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="文件大小">
                  <el-input v-model="ulinkerSystem.fileSize" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="下载地址">
                  <el-input v-model="ulinkerSystem.downloadUrl" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MD5">
                  <el-input v-model="ulinkerSystem.md5" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider />
          </div>

          <div v-for="(teair, index) of targetedConfig.teair" :key="`teair${index}`">
            <div v-if="index===0">
              <h2 class="targetedTitle">teair</h2>
              <el-button type="primary" size="mini" @click="targetedConfig.teair.push({deviceIds: [], region: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: ''})">添加</el-button>
              <el-button type="danger" size="mini" @click="deleteConfig(targetedConfig.teair)">删除</el-button>
            </div>
            <el-row>
              <el-col :span="6">
                <el-form-item label="teair sn">
                  <el-select
                    v-model="teair.deviceIds"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    :disabled="!!teair.region.length"
                  >
                    <el-option
                      v-for="item in teair.deviceIds"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="teair系统当前版本号">
                  <el-input v-model="teair.current" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最低版本号">
                  <el-input v-model="teair.min" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="文件大小">
                  <el-input v-model="teair.fileSize" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="法规地区">
                  <el-select
                    v-model="teair.region"
                    multiple
                    :disabled="!!teair.deviceIds.length"
                  >
                    <el-option
                      v-for="item in regulationsAreaCodeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="14">
                <el-form-item label="下载地址" label-width="75px">
                  <el-input v-model="teair.downloadUrl" />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="MD5" label-width="50px">
                  <el-input v-model="teair.md5" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <el-form-item style="float: right;">
            <el-button type="primary" @click="handleUpdate('targeted', targetedConfig)">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="安卓通用版本配置" name="androidVersion">
        <el-form ref="form" :model="androidVersionConfig[0]" label-width="150px" :inline="false" size="normal">
          <div v-for="(androidConfig, index) in androidVersionConfig" :key="`androidConfig${index}`">
            <div v-if="index===0">
              <el-button type="primary" size="mini" @click="androidVersionConfig.push({ packageName: '', appChannel: '', current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' })">添加</el-button>
              <el-button type="danger" size="mini" @click="deleteConfig(androidVersionConfig)">删除</el-button>
            </div>

            <el-row>
              <el-col :span="6">
                <el-form-item label="包名">
                  <el-input
                    v-model="androidConfig.packageName"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="渠道">
                  <el-input
                    v-model="androidConfig.appChannel"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="当前版本号">
                  <el-input v-model="androidConfig.current" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最低版本号">
                  <el-input v-model="androidConfig.min" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="下载地址">
                  <el-input v-model="androidConfig.downloadUrl" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="文件大小">
                  <el-input v-model="androidConfig.fileSize" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="MD5">
                  <el-input v-model="androidConfig.md5" />
                </el-form-item>
              </el-col>
              <!-- <UploadToOss /> -->
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6" :offset="0">
                <el-form-item label="法规地区">
                  <el-select
                    v-model="androidConfig.areaCode"
                    multiple
                    :disabled="!!androidConfig.deviceIds.length"
                  >
                    <el-option
                      v-for="item in regulationsAreaCodeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="18" :offset="0">
                <el-form-item label="设备唯一编码">
                  <el-select
                    v-model="androidConfig.deviceIds"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    :disabled="!!androidConfig.areaCode.length"
                  >
                    <el-option
                      v-for="item in androidConfig.deviceIds"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider />
          </div>
          <el-form-item style="float: right;">
            <el-button type="primary" @click="handleUpdateList('androidVersion', androidVersionConfig)">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="开发功能" name="devFunc">
        <el-row>
          <el-col :span="12">
            <el-button type="primary" size="default" @click="liveStatisImmediately">统计直播数据</el-button>
          </el-col>
        </el-row>
        <el-divider />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getInfo, getList, updateConfig, addRegionPermissionConfig } from '@/api/config'
import { liveStatis } from '@/api/statistic'
import UploadToOss from '@/components/Upload'
import { getCountryMap, getCountryDetailMap } from '../../utils/countryMap'
const functionList = [
  { label: '直播', value: 'live' },
  { label: '图书馆', value: 'library' },
  { label: '云端统计', value: 'cloudStatistic' },
  { label: '乳腺病例库', value: 'breastCases' },
  { label: '小麦同学', value: 'breastAI' },
  { label: '群落', value: 'groupset' },
  { label: '微信', value: 'wechat' },
  { label: '产科AI', value: 'obstetricalAI' },
  { label: 'DR助手', value: 'drAI' },
  { label: 'BI统计', value: 'qcStatistics' },
  { label: '电视墙', value: 'tvwall' },
  { label: '邀请码', value: 'referralCode' },
  { label: 'web推桌面', value: 'webShareScreen' },
  { label: 'web电视墙进入会话', value: 'webTvwallEnterConversation' },
  { label: 'ai', value: 'ai' }
]
const regulationsAreaCodeOptions = [
  { label: '美国(FDA)', value: 'FDA' },
  { label: '加拿大(FDA_CA)', value: 'FDA_CA' },
  { label: '中国(SFDA)', value: 'SFDA' },
  { label: '俄罗斯', value: 'RU' },
  { label: '巴西', value: 'BR' },
  { label: '日本', value: 'JP' },
  { label: '其他', value: 'OT' }
]
const functionMap = { live: 1, library: 1, cloudStatistic: 1, breastCases: 1, breastAI: 1, groupset: 1, wechat: 1, obstetricalAI: 1, qcStatistics: 1, tvwall: 1, referralCode: 1, webShareScreen: 1, webTvwallEnterConversation: 1 }
export default {
  name: 'ConfigCenter',
  components: { UploadToOss },
  filters: {
    regionFilter(region) {
      return getCountryMap[region]
    },
    forbidModuleFilter(array) {
      const map = {}
      functionList.forEach((item) => {
        map[item.value] = item.label
      })
      return (array.map((item) => map[item])).toString()
    }
  },
  data() {
    return {
      dialogStatus: 'create',
      dialogTitle: {
        create: '新增区域配置',
        update: '修改区域配置'
      },
      functionList,
      regulationsAreaCodeOptions,
      checkedFunctionModule: '',
      activeName: 'version',
      listLoading: false,
      tsKey: 0,
      regionForm: { code: '', forbidModule: [] },
      checkAll: false,
      isIndeterminate: true,
      selected: '',
      countryDetailMap: getCountryDetailMap,
      regionPermissionConfig: [],
      regionCountryCodeArray: [],
      dialogVisible: false,
      envConfig: { announcement: {}},
      devConfig: { showStatisticUserIdList: '' },
      versionConfig: { pc: { downloadUrl: '' }, pc_anti_theft: { downloadUrl: '' }, ios: {}, android: { downloadUrl: '' }, androidGooglePlay: { downloadUrl: '' }, ulinker: { downloadUrl: '' }, ulinkerCE: {}, ulinkerSystem: { current: '', min: '', fileSize: 0, title: '', md5: '', newFeature: '', publishTime: '', downloadUrl: '' }, teair: { current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' }},
      androidVersionConfig: [{ deviceIds: [], areaCode: [], packageName: '', appChannel: '', current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' }],
      targetedConfig: { ulinker: [{ deviceIds: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' }], ulinkerSystem: [{ deviceIds: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' }], teair: [{ deviceIds: [], region: [], current: '', min: '', fileSize: 0, md5: '', downloadUrl: '' }] },
      dopplerUpgrade: [{ series_number: '', regulation_type: '', product_name: '', operation_system: '', software_version: '', min_support_version: '', target_version: '', download_url: '', description: '', md5: '', permission: {}, force_upgrade: false }]
    }
  },
  created() {
    this.getConfigData()
  },
  methods: {
    async getConfigData() {
      const envConfig = await getInfo({ type: 'env' })
      this.envConfig = { ...this.envConfig, ...envConfig }

      const devConfig = await getInfo({ type: 'dev' })
      this.devConfig = { ...this.devConfig, ...devConfig }

      const versionData = await getInfo({ type: 'version' })
      this.versionConfig = Object.assign({}, this.versionConfig, versionData)

      const targetedData = await getInfo({ type: 'targeted' })
      this.targetedConfig = Object.assign({}, this.targetedConfig, targetedData)
      const androidData = await getList({ type: 'androidVersion' })
      this.androidVersionConfig = Object.assign([], this.androidVersionConfig, androidData)
      await this.getRegionPermissionConfig()
    },
    handleUpdate(type, data) {
      updateConfig({ type, data }).then(() => {
        this.getConfigData()
        this.$message({
          message: '成功',
          type: 'success'
        })
      })
    },
    handleUpdateList(type, list) {
      updateConfig({ type, list }).then(() => {
        this.getConfigData()
        this.$message({
          message: '成功',
          type: 'success'
        })
      })
    },
    liveStatisImmediately() {
      liveStatis().then(() => {
        this.$message({
          message: '成功',
          type: 'success'
        })
      })
    },
    async getRegionPermissionConfig() {
      const regionPermissionData = await getInfo({ type: 'regionPermission' })
      const array = []
      for (const code in regionPermissionData) {
        const forbidModule = []
        for (const func in regionPermissionData[code]) {
          if (!regionPermissionData[code][func]) {
            forbidModule.push(func)
          }
        }
        array.push({ code, forbidModule })
      }
      this.regionPermissionConfig = array
      this.regionCountryCodeArray = this.regionPermissionConfig.map((item) => item.code)
    },
    handleAddRegionPermission() {
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.regionForm = { code: '', forbidModule: [] }
      this.tsKey = Date.now()
    },
    countryDisabled(code) {
      return this.regionCountryCodeArray.includes(code)
    },
    async createRegionPermission() {
      await addRegionPermissionConfig(this.regionForm)
      await this.getRegionPermissionConfig()
      this.dialogVisible = false
    },
    handleUpdateRegionPermission(config) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.regionForm = { ...config }
      this.tsKey = Date.now()
    },
    async updateRegionPermission() {
      const data = {}
      for (const item of this.regionPermissionConfig) {
        if (item.code === this.regionForm.code) {
          item.forbidModule = this.regionForm.forbidModule
        }
        data[item.code] = { ...functionMap }
        for (const key of item.forbidModule) {
          data[item.code][key] = 0
        }
      }
      await updateConfig({ type: 'regionPermission', data })
      this.$message({ type: 'success', message: '修改成功' })
      await this.getRegionPermissionConfig()
      this.dialogVisible = false
    },
    async handleDeleteRegionPermission(code) {
      this.$confirm('请确认此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const array = []
        for (const item of this.regionPermissionConfig) {
          if (item.code !== code) {
            array.push(item)
          }
        }
        const data = {}
        for (const item of array) {
          data[item.code] = { ...functionMap }
          for (const key of item.forbidModule) {
            data[item.code][key] = 0
          }
        }
        await updateConfig({ type: 'regionPermission', data })
        await this.getRegionPermissionConfig()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    deleteConfig(data) {
      if (data.length < 2) {
        this.$message({ type: 'danger', message: '配置项应大于一项' })
      } else {
        data.pop()
      }
    },
    onUpload({ fileSize, downloadUrl, md5, key }) {
      console.log('onUpload', fileSize, downloadUrl, md5, key)
      this.versionConfig[key].fileSize = fileSize
      this.versionConfig[key].downloadUrl = downloadUrl
      this.versionConfig[key].md5 = md5
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.targetedTitle {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  margin-right: 5px;
}
</style>
