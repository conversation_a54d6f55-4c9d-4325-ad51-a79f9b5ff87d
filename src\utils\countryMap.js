module.exports = {
  get getCountryMap() {
    const map = {}
    for (const item of dataArray) {
      map[item.short] = item.name
    }
    return map
  },
  get getCountryDetailMap() {
    const map = {}
    for (const item of dataArray) {
      map[item.short] = {
        zhName: item.name,
        enName: item.en,
        code: item.short
      }
    }
    return map
  }
}
const dataArray = [
  {
    'en': 'default',
    'short': 'default',
    'shortLen3': 'DEF',
    'numberCode': '000',
    'name': '默认'
  },
  {
    'en': 'Afghanistan',
    'short': 'AF',
    'shortLen3': 'AFG',
    'numberCode': '004',
    'iso': 'ISO 3166-2:AF',
    'name': '阿富汗',
    'isTndependent': true,
    'tel': '93'
  },
  {
    'en': 'Åland Islands',
    'short': 'AX',
    'shortLen3': 'ALA',
    'numberCode': '248',
    'iso': 'ISO 3166-2:AX',
    'name': '奥兰',
    'isTndependent': false
  },
  {
    'en': 'Albania',
    'short': 'AL',
    'shortLen3': 'ALB',
    'numberCode': '008',
    'iso': 'ISO 3166-2:AL',
    'name': '阿尔巴尼亚',
    'isTndependent': true,
    'tel': '355'
  },
  {
    'en': 'Algeria',
    'short': 'DZ',
    'shortLen3': 'DZA',
    'numberCode': '012',
    'iso': 'ISO 3166-2:DZ',
    'name': '阿尔及利亚',
    'isTndependent': true,
    'tel': '213'
  },
  {
    'en': 'American Samoa',
    'short': 'AS',
    'shortLen3': 'ASM',
    'numberCode': '016',
    'iso': 'ISO 3166-2:AS',
    'name': '美属萨摩亚',
    'isTndependent': false,
    'tel': '1684'
  },
  {
    'en': 'Andorra',
    'short': 'AD',
    'shortLen3': 'AND',
    'numberCode': '020',
    'iso': 'ISO 3166-2:AD',
    'name': '安道尔',
    'isTndependent': true,
    'tel': '376'
  },
  {
    'en': 'Angola',
    'short': 'AO',
    'shortLen3': 'AGO',
    'numberCode': '024',
    'iso': 'ISO 3166-2:AO',
    'name': '安哥拉',
    'isTndependent': true,
    'tel': '244'
  },
  {
    'en': 'Anguilla',
    'short': 'AI',
    'shortLen3': 'AIA',
    'numberCode': '660',
    'iso': 'ISO 3166-2:AI',
    'name': '安圭拉',
    'isTndependent': false,
    'tel': '1264'
  },
  {
    'en': 'Antarctica',
    'short': 'AQ',
    'shortLen3': 'ATA',
    'numberCode': '010',
    'iso': 'ISO 3166-2:AQ',
    'name': '南极洲',
    'isTndependent': false,
    'tel': '672'
  },
  {
    'en': 'Antigua and Barbuda',
    'short': 'AG',
    'shortLen3': 'ATG',
    'numberCode': '028',
    'iso': 'ISO 3166-2:AG',
    'name': '安提瓜和巴布达',
    'isTndependent': true,
    'tel': '1268'
  },
  {
    'en': 'Argentina',
    'short': 'AR',
    'shortLen3': 'ARG',
    'numberCode': '032',
    'iso': 'ISO 3166-2:AR',
    'name': '阿根廷',
    'isTndependent': true,
    'tel': '54'
  },
  {
    'en': 'Armenia',
    'short': 'AM',
    'shortLen3': 'ARM',
    'numberCode': '051',
    'iso': 'ISO 3166-2:AM',
    'name': '亚美尼亚',
    'isTndependent': true,
    'tel': '374'
  },
  {
    'en': 'Aruba',
    'short': 'AW',
    'shortLen3': 'ABW',
    'numberCode': '533',
    'iso': 'ISO 3166-2:AW',
    'name': '阿鲁巴',
    'isTndependent': false,
    'tel': '297'
  },
  {
    'en': 'Australia',
    'short': 'AU',
    'shortLen3': 'AUS',
    'numberCode': '036',
    'iso': 'ISO 3166-2:AU',
    'name': '澳大利亚',
    'isTndependent': true,
    'tel': '61'
  },
  {
    'en': 'Austria',
    'short': 'AT',
    'shortLen3': 'AUT',
    'numberCode': '040',
    'iso': 'ISO 3166-2:AT',
    'name': '奥地利',
    'isTndependent': true,
    'tel': '43'
  },
  {
    'en': 'Azerbaijan',
    'short': 'AZ',
    'shortLen3': 'AZE',
    'numberCode': '031',
    'iso': 'ISO 3166-2:AZ',
    'name': '阿塞拜疆',
    'isTndependent': true,
    'tel': '994'
  },
  {
    'en': 'Bahamas',
    'short': 'BS',
    'shortLen3': 'BHS',
    'numberCode': '044',
    'iso': 'ISO 3166-2:BS',
    'name': '巴哈马',
    'isTndependent': true,
    'tel': '1242'
  },
  {
    'en': 'Bahrain',
    'short': 'BH',
    'shortLen3': 'BHR',
    'numberCode': '048',
    'iso': 'ISO 3166-2:BH',
    'name': '巴林',
    'isTndependent': true,
    'tel': '973'
  },
  {
    'en': 'Bangladesh',
    'short': 'BD',
    'shortLen3': 'BGD',
    'numberCode': '050',
    'iso': 'ISO 3166-2:BD',
    'name': '孟加拉国',
    'isTndependent': true,
    'tel': '880'
  },
  {
    'en': 'Barbados',
    'short': 'BB',
    'shortLen3': 'BRB',
    'numberCode': '052',
    'iso': 'ISO 3166-2:BB',
    'name': '巴巴多斯',
    'isTndependent': true,
    'tel': '1246'
  },
  {
    'en': 'Belarus',
    'short': 'BY',
    'shortLen3': 'BLR',
    'numberCode': '112',
    'iso': 'ISO 3166-2:BY',
    'name': '白俄罗斯',
    'isTndependent': true,
    'tel': '375'
  },
  {
    'en': 'Belgium',
    'short': 'BE',
    'shortLen3': 'BEL',
    'numberCode': '056',
    'iso': 'ISO 3166-2:BE',
    'name': '比利时',
    'isTndependent': true,
    'tel': '32'
  },
  {
    'en': 'Belize',
    'short': 'BZ',
    'shortLen3': 'BLZ',
    'numberCode': '084',
    'iso': 'ISO 3166-2:BZ',
    'name': '伯利兹',
    'isTndependent': true,
    'tel': '501'
  },
  {
    'en': 'Benin',
    'short': 'BJ',
    'shortLen3': 'BEN',
    'numberCode': '204',
    'iso': 'ISO 3166-2:BJ',
    'name': '贝宁',
    'isTndependent': true,
    'tel': '229'
  },
  {
    'en': 'Bermuda',
    'short': 'BM',
    'shortLen3': 'BMU',
    'numberCode': '060',
    'iso': 'ISO 3166-2:BM',
    'name': '百慕大',
    'isTndependent': false,
    'tel': '1441'
  },
  {
    'en': 'Bhutan',
    'short': 'BT',
    'shortLen3': 'BTN',
    'numberCode': '064',
    'iso': 'ISO 3166-2:BT',
    'name': '不丹',
    'isTndependent': true,
    'tel': '975'
  },
  {
    'en': 'Bolivia (Plurinational State of)',
    'short': 'BO',
    'shortLen3': 'BOL',
    'numberCode': '068',
    'iso': 'ISO 3166-2:BO',
    'name': '玻利维亚',
    'isTndependent': true,
    'tel': '591'
  },
  {
    'en': 'Bonaire, Sint Eustatius and Saba',
    'short': 'BQ',
    'shortLen3': 'BES',
    'numberCode': '535',
    'iso': 'ISO 3166-2:BQ',
    'name': '荷兰加勒比区',
    'isTndependent': false
  },
  {
    'en': 'Bosnia and Herzegovina',
    'short': 'BA',
    'shortLen3': 'BIH',
    'numberCode': '070',
    'iso': 'ISO 3166-2:BA',
    'name': '波黑',
    'isTndependent': true,
    'tel': '387'
  },
  {
    'en': 'Botswana',
    'short': 'BW',
    'shortLen3': 'BWA',
    'numberCode': '072',
    'iso': 'ISO 3166-2:BW',
    'name': '博茨瓦纳',
    'isTndependent': true,
    'tel': '267'
  },
  {
    'en': 'Bouvet Island',
    'short': 'BV',
    'shortLen3': 'BVT',
    'numberCode': '074',
    'iso': 'ISO 3166-2:BV',
    'name': '布韦岛',
    'isTndependent': false
  },
  {
    'en': 'Brazil',
    'short': 'BR',
    'shortLen3': 'BRA',
    'numberCode': '076',
    'iso': 'ISO 3166-2:BR',
    'name': '巴西',
    'isTndependent': true,
    'tel': '55'
  },
  {
    'en': 'British Indian Ocean Territory',
    'short': 'IO',
    'shortLen3': 'IOT',
    'numberCode': '086',
    'iso': 'ISO 3166-2:IO',
    'name': '英属印度洋领地',
    'isTndependent': false
  },
  {
    'en': 'Brunei Darussalam',
    'short': 'BN',
    'shortLen3': 'BRN',
    'numberCode': '096',
    'iso': 'ISO 3166-2:BN',
    'name': '文莱',
    'isTndependent': true,
    'tel': '673'
  },
  {
    'en': 'Bulgaria',
    'short': 'BG',
    'shortLen3': 'BGR',
    'numberCode': '100',
    'iso': 'ISO 3166-2:BG',
    'name': '保加利亚',
    'isTndependent': true,
    'tel': '359'
  },
  {
    'en': 'Burkina Faso',
    'short': 'BF',
    'shortLen3': 'BFA',
    'numberCode': '854',
    'iso': 'ISO 3166-2:BF',
    'name': '布基纳法索',
    'isTndependent': true,
    'tel': '226'
  },
  {
    'en': 'Burundi',
    'short': 'BI',
    'shortLen3': 'BDI',
    'numberCode': '108',
    'iso': 'ISO 3166-2:BI',
    'name': '布隆迪',
    'isTndependent': true,
    'tel': '257'
  },
  {
    'en': 'Cabo Verde',
    'short': 'CV',
    'shortLen3': 'CPV',
    'numberCode': '132',
    'iso': 'ISO 3166-2:CV',
    'name': '佛得角',
    'isTndependent': true,
    'tel': '238'
  },
  {
    'en': 'Cambodia',
    'short': 'KH',
    'shortLen3': 'KHM',
    'numberCode': '116',
    'iso': 'ISO 3166-2:KH',
    'name': '柬埔寨',
    'isTndependent': true,
    'tel': '855'
  },
  {
    'en': 'Cameroon',
    'short': 'CM',
    'shortLen3': 'CMR',
    'numberCode': '120',
    'iso': 'ISO 3166-2:CM',
    'name': '喀麦隆',
    'isTndependent': true,
    'tel': '237'
  },
  {
    'en': 'Canada',
    'short': 'CA',
    'shortLen3': 'CAN',
    'numberCode': '124',
    'iso': 'ISO 3166-2:CA',
    'name': '加拿大',
    'isTndependent': true,
    'tel': '1'
  },
  {
    'en': 'Cayman Islands',
    'short': 'KY',
    'shortLen3': 'CYM',
    'numberCode': '136',
    'iso': 'ISO 3166-2:KY',
    'name': '开曼群岛',
    'isTndependent': false,
    'tel': '1345'
  },
  {
    'en': 'Central African Republic',
    'short': 'CF',
    'shortLen3': 'CAF',
    'numberCode': '140',
    'iso': 'ISO 3166-2:CF',
    'name': '中非',
    'isTndependent': true,
    'tel': '236'
  },
  {
    'en': 'Chad',
    'short': 'TD',
    'shortLen3': 'TCD',
    'numberCode': '148',
    'iso': 'ISO 3166-2:TD',
    'name': '乍得',
    'isTndependent': true,
    'tel': '235'
  },
  {
    'en': 'Chile',
    'short': 'CL',
    'shortLen3': 'CHL',
    'numberCode': '152',
    'iso': 'ISO 3166-2:CL',
    'name': '智利',
    'isTndependent': true,
    'tel': '56'
  },
  {
    'en': 'China',
    'short': 'CN',
    'shortLen3': 'CHN',
    'numberCode': '156',
    'iso': 'ISO 3166-2:CN',
    'name': '中国',
    'isTndependent': true,
    'tel': '86'
  },
  {
    'en': 'Christmas Island',
    'short': 'CX',
    'shortLen3': 'CXR',
    'numberCode': '162',
    'iso': 'ISO 3166-2:CX',
    'name': '圣诞岛',
    'isTndependent': false,
    'tel': '61'
  },
  {
    'en': 'Cocos (Keeling) Islands',
    'short': 'CC',
    'shortLen3': 'CCK',
    'numberCode': '166',
    'iso': 'ISO 3166-2:CC',
    'name': '科科斯（基林）群岛',
    'isTndependent': false,
    'tel': '61'
  },
  {
    'en': 'Colombia',
    'short': 'CO',
    'shortLen3': 'COL',
    'numberCode': '170',
    'iso': 'ISO 3166-2:CO',
    'name': '哥伦比亚',
    'isTndependent': true,
    'tel': '57'
  },
  {
    'en': 'Comoros',
    'short': 'KM',
    'shortLen3': 'COM',
    'numberCode': '174',
    'iso': 'ISO 3166-2:KM',
    'name': '科摩罗',
    'isTndependent': true,
    'tel': '269'
  },
  {
    'en': 'Congo',
    'short': 'CG',
    'shortLen3': 'COG',
    'numberCode': '178',
    'iso': 'ISO 3166-2:CG',
    'name': '刚果共和国',
    'isTndependent': true,
    'tel': '242'
  },
  {
    'en': 'Congo (Democratic Republic of the)',
    'short': 'CD',
    'shortLen3': 'COD',
    'numberCode': '180',
    'iso': 'ISO 3166-2:CD',
    'name': '刚果民主共和国',
    'isTndependent': true,
    'tel': '243'
  },
  {
    'en': 'Cook Islands',
    'short': 'CK',
    'shortLen3': 'COK',
    'numberCode': '184',
    'iso': 'ISO 3166-2:CK',
    'name': '库克群岛',
    'isTndependent': false,
    'tel': '682'
  },
  {
    'en': 'Costa Rica',
    'short': 'CR',
    'shortLen3': 'CRI',
    'numberCode': '188',
    'iso': 'ISO 3166-2:CR',
    'name': '哥斯达黎加',
    'isTndependent': true,
    'tel': '506'
  },
  {
    'en': "Côte d'Ivoire",
    'short': 'CI',
    'shortLen3': 'CIV',
    'numberCode': '384',
    'iso': 'ISO 3166-2:CI',
    'name': '科特迪瓦',
    'isTndependent': true,
    'tel': '225'
  },
  {
    'en': 'Croatia',
    'short': 'HR',
    'shortLen3': 'HRV',
    'numberCode': '191',
    'iso': 'ISO 3166-2:HR',
    'name': '克罗地亚',
    'isTndependent': true,
    'tel': '385'
  },
  {
    'en': 'Cuba',
    'short': 'CU',
    'shortLen3': 'CUB',
    'numberCode': '192',
    'iso': 'ISO 3166-2:CU',
    'name': '古巴',
    'isTndependent': true,
    'tel': '53'
  },
  {
    'en': 'Curacao !Curaçao',
    'short': 'CW',
    'shortLen3': 'CUW',
    'numberCode': '531',
    'iso': 'ISO 3166-2:CW',
    'name': '库拉索',
    'isTndependent': false
  },
  {
    'en': 'Cyprus',
    'short': 'CY',
    'shortLen3': 'CYP',
    'numberCode': '196',
    'iso': 'ISO 3166-2:CY',
    'name': '塞浦路斯',
    'isTndependent': true,
    'tel': '357'
  },
  {
    'en': 'Czechia',
    'short': 'CZ',
    'shortLen3': 'CZE',
    'numberCode': '203',
    'iso': 'ISO 3166-2:CZ',
    'name': '捷克',
    'isTndependent': true,
    'tel': '420'
  },
  {
    'en': 'Denmark',
    'short': 'DK',
    'shortLen3': 'DNK',
    'numberCode': '208',
    'iso': 'ISO 3166-2:DK',
    'name': '丹麦',
    'isTndependent': true,
    'tel': '45'
  },
  {
    'en': 'Djibouti',
    'short': 'DJ',
    'shortLen3': 'DJI',
    'numberCode': '262',
    'iso': 'ISO 3166-2:DJ',
    'name': '吉布提',
    'isTndependent': true,
    'tel': '253'
  },
  {
    'en': 'Dominica',
    'short': 'DM',
    'shortLen3': 'DMA',
    'numberCode': '212',
    'iso': 'ISO 3166-2:DM',
    'name': '多米尼克',
    'isTndependent': true,
    'tel': '1767'
  },
  {
    'en': 'Dominican Republic',
    'short': 'DO',
    'shortLen3': 'DOM',
    'numberCode': '214',
    'iso': 'ISO 3166-2:DO',
    'name': '多米尼加',
    'isTndependent': true,
    'tel': '1809'
  },
  {
    'en': 'Ecuador',
    'short': 'EC',
    'shortLen3': 'ECU',
    'numberCode': '218',
    'iso': 'ISO 3166-2:EC',
    'name': '厄瓜多尔',
    'isTndependent': true,
    'tel': '593'
  },
  {
    'en': 'Egypt',
    'short': 'EG',
    'shortLen3': 'EGY',
    'numberCode': '818',
    'iso': 'ISO 3166-2:EG',
    'name': '埃及',
    'isTndependent': true,
    'tel': '20'
  },
  {
    'en': 'El Salvador',
    'short': 'SV',
    'shortLen3': 'SLV',
    'numberCode': '222',
    'iso': 'ISO 3166-2:SV',
    'name': '萨尔瓦多',
    'isTndependent': true,
    'tel': '503'
  },
  {
    'en': 'Equatorial Guinea',
    'short': 'GQ',
    'shortLen3': 'GNQ',
    'numberCode': '226',
    'iso': 'ISO 3166-2:GQ',
    'name': '赤道几内亚',
    'isTndependent': true,
    'tel': '240'
  },
  {
    'en': 'Eritrea',
    'short': 'ER',
    'shortLen3': 'ERI',
    'numberCode': '232',
    'iso': 'ISO 3166-2:ER',
    'name': '厄立特里亚',
    'isTndependent': true,
    'tel': '291'
  },
  {
    'en': 'Estonia',
    'short': 'EE',
    'shortLen3': 'EST',
    'numberCode': '233',
    'iso': 'ISO 3166-2:EE',
    'name': '爱沙尼亚',
    'isTndependent': true,
    'tel': '372'
  },
  {
    'en': 'Eswatini',
    'short': 'SZ',
    'shortLen3': 'SWZ',
    'numberCode': '748',
    'iso': 'ISO 3166-2:SZ',
    'name': '斯威士兰',
    'isTndependent': true,
    'tel': '268'
  },
  {
    'en': 'Ethiopia',
    'short': 'ET',
    'shortLen3': 'ETH',
    'numberCode': '231',
    'iso': 'ISO 3166-2:ET',
    'name': '埃塞俄比亚',
    'isTndependent': true,
    'tel': '251'
  },
  {
    'en': 'Falkland Islands (Malvinas)',
    'short': 'FK',
    'shortLen3': 'FLK',
    'numberCode': '238',
    'iso': 'ISO 3166-2:FK',
    'name': '福克兰群岛',
    'isTndependent': false,
    'tel': '500'
  },
  {
    'en': 'Faroe Islands',
    'short': 'FO',
    'shortLen3': 'FRO',
    'numberCode': '234',
    'iso': 'ISO 3166-2:FO',
    'name': '法罗群岛',
    'isTndependent': false,
    'tel': '298'
  },
  {
    'en': 'Fiji',
    'short': 'FJ',
    'shortLen3': 'FJI',
    'numberCode': '242',
    'iso': 'ISO 3166-2:FJ',
    'name': '斐济',
    'isTndependent': true,
    'tel': '679'
  },
  {
    'en': 'Finland',
    'short': 'FI',
    'shortLen3': 'FIN',
    'numberCode': '246',
    'iso': 'ISO 3166-2:FI',
    'name': '芬兰',
    'isTndependent': true,
    'tel': '358'
  },
  {
    'en': 'France',
    'short': 'FR',
    'shortLen3': 'FRA',
    'numberCode': '250',
    'iso': 'ISO 3166-2:FR',
    'name': '法国',
    'isTndependent': true,
    'tel': '33'
  },
  {
    'en': 'French Guiana',
    'short': 'GF',
    'shortLen3': 'GUF',
    'numberCode': '254',
    'iso': 'ISO 3166-2:GF',
    'name': '法属圭亚那',
    'isTndependent': false,
    'tel': '594'
  },
  {
    'en': 'French Polynesia',
    'short': 'PF',
    'shortLen3': 'PYF',
    'numberCode': '258',
    'iso': 'ISO 3166-2:PF',
    'name': '法属波利尼西亚',
    'isTndependent': false,
    'tel': '689'
  },
  {
    'en': 'French Southern Territories',
    'short': 'TF',
    'shortLen3': 'ATF',
    'numberCode': '260',
    'iso': 'ISO 3166-2:TF',
    'name': '法属南部和南极领地',
    'isTndependent': false
  },
  {
    'en': 'Gabon',
    'short': 'GA',
    'shortLen3': 'GAB',
    'numberCode': '266',
    'iso': 'ISO 3166-2:GA',
    'name': '加蓬',
    'isTndependent': true,
    'tel': '241'
  },
  {
    'en': 'Gambia',
    'short': 'GM',
    'shortLen3': 'GMB',
    'numberCode': '270',
    'iso': 'ISO 3166-2:GM',
    'name': '冈比亚',
    'isTndependent': true,
    'tel': '220'
  },
  {
    'en': 'Georgia',
    'short': 'GE',
    'shortLen3': 'GEO',
    'numberCode': '268',
    'iso': 'ISO 3166-2:GE',
    'name': '格鲁吉亚',
    'isTndependent': true,
    'tel': '995'
  },
  {
    'en': 'Germany',
    'short': 'DE',
    'shortLen3': 'DEU',
    'numberCode': '276',
    'iso': 'ISO 3166-2:DE',
    'name': '德国',
    'isTndependent': true,
    'tel': '49'
  },
  {
    'en': 'Ghana',
    'short': 'GH',
    'shortLen3': 'GHA',
    'numberCode': '288',
    'iso': 'ISO 3166-2:GH',
    'name': '加纳',
    'isTndependent': true,
    'tel': '233'
  },
  {
    'en': 'Gibraltar',
    'short': 'GI',
    'shortLen3': 'GIB',
    'numberCode': '292',
    'iso': 'ISO 3166-2:GI',
    'name': '直布罗陀',
    'isTndependent': false,
    'tel': '350'
  },
  {
    'en': 'Greece',
    'short': 'GR',
    'shortLen3': 'GRC',
    'numberCode': '300',
    'iso': 'ISO 3166-2:GR',
    'name': '希腊',
    'isTndependent': true,
    'tel': '30'
  },
  {
    'en': 'Greenland',
    'short': 'GL',
    'shortLen3': 'GRL',
    'numberCode': '304',
    'iso': 'ISO 3166-2:GL',
    'name': '格陵兰',
    'isTndependent': false,
    'tel': '299'
  },
  {
    'en': 'Grenada',
    'short': 'GD',
    'shortLen3': 'GRD',
    'numberCode': '308',
    'iso': 'ISO 3166-2:GD',
    'name': '格林纳达',
    'isTndependent': true,
    'tel': '1473'
  },
  {
    'en': 'Guadeloupe',
    'short': 'GP',
    'shortLen3': 'GLP',
    'numberCode': '312',
    'iso': 'ISO 3166-2:GP',
    'name': '瓜德罗普',
    'isTndependent': false,
    'tel': '590'
  },
  {
    'en': 'Guam',
    'short': 'GU',
    'shortLen3': 'GUM',
    'numberCode': '316',
    'iso': 'ISO 3166-2:GU',
    'name': '关岛',
    'isTndependent': false,
    'tel': '1671'
  },
  {
    'en': 'Guatemala',
    'short': 'GT',
    'shortLen3': 'GTM',
    'numberCode': '320',
    'iso': 'ISO 3166-2:GT',
    'name': '危地马拉',
    'isTndependent': true,
    'tel': '502'
  },
  {
    'en': 'Guernsey',
    'short': 'GG',
    'shortLen3': 'GGY',
    'numberCode': '831',
    'iso': 'ISO 3166-2:GG',
    'name': '根西',
    'isTndependent': false,
    'tel': '1481'
  },
  {
    'en': 'Guinea',
    'short': 'GN',
    'shortLen3': 'GIN',
    'numberCode': '324',
    'iso': 'ISO 3166-2:GN',
    'name': '几内亚',
    'isTndependent': true,
    'tel': '224'
  },
  {
    'en': 'Guinea-Bissau',
    'short': 'GW',
    'shortLen3': 'GNB',
    'numberCode': '624',
    'iso': 'ISO 3166-2:GW',
    'name': '几内亚比绍',
    'isTndependent': true,
    'tel': '245'
  },
  {
    'en': 'Guyana',
    'short': 'GY',
    'shortLen3': 'GUY',
    'numberCode': '328',
    'iso': 'ISO 3166-2:GY',
    'name': '圭亚那',
    'isTndependent': true,
    'tel': '592'
  },
  {
    'en': 'Haiti',
    'short': 'HT',
    'shortLen3': 'HTI',
    'numberCode': '332',
    'iso': 'ISO 3166-2:HT',
    'name': '海地',
    'isTndependent': true,
    'tel': '509'
  },
  {
    'en': 'Heard Island and McDonald Islands',
    'short': 'HM',
    'shortLen3': 'HMD',
    'numberCode': '334',
    'iso': 'ISO 3166-2:HM',
    'name': '赫德岛和麦克唐纳群岛',
    'isTndependent': false
  },
  {
    'en': 'Holy See',
    'short': 'VA',
    'shortLen3': 'VAT',
    'numberCode': '336',
    'iso': 'ISO 3166-2:VA',
    'name': '梵蒂冈',
    'isTndependent': true,
    'tel': '39'
  },
  {
    'en': 'Honduras',
    'short': 'HN',
    'shortLen3': 'HND',
    'numberCode': '340',
    'iso': 'ISO 3166-2:HN',
    'name': '洪都拉斯',
    'isTndependent': true,
    'tel': '504'
  },
  {
    'en': 'Hong Kong',
    'short': 'HK',
    'shortLen3': 'HKG',
    'numberCode': '344',
    'iso': 'ISO 3166-2:HK',
    'name': '香港',
    'isTndependent': false,
    'tel': '852'
  },
  {
    'en': 'Hungary',
    'short': 'HU',
    'shortLen3': 'HUN',
    'numberCode': '348',
    'iso': 'ISO 3166-2:HU',
    'name': '匈牙利',
    'isTndependent': true,
    'tel': '36'
  },
  {
    'en': 'Iceland',
    'short': 'IS',
    'shortLen3': 'ISL',
    'numberCode': '352',
    'iso': 'ISO 3166-2:IS',
    'name': '冰岛',
    'isTndependent': true,
    'tel': '354'
  },
  {
    'en': 'India',
    'short': 'IN',
    'shortLen3': 'IND',
    'numberCode': '356',
    'iso': 'ISO 3166-2:IN',
    'name': '印度',
    'isTndependent': true,
    'tel': '91'
  },
  {
    'en': 'Indonesia',
    'short': 'ID',
    'shortLen3': 'IDN',
    'numberCode': '360',
    'iso': 'ISO 3166-2:ID',
    'name': '印度尼西亚',
    'isTndependent': true,
    'tel': '62'
  },
  {
    'en': 'Iran (Islamic Republic of)',
    'short': 'IR',
    'shortLen3': 'IRN',
    'numberCode': '364',
    'iso': 'ISO 3166-2:IR',
    'name': '伊朗',
    'isTndependent': true,
    'tel': '98'
  },
  {
    'en': 'Iraq',
    'short': 'IQ',
    'shortLen3': 'IRQ',
    'numberCode': '368',
    'iso': 'ISO 3166-2:IQ',
    'name': '伊拉克',
    'isTndependent': true,
    'tel': '964'
  },
  {
    'en': 'Ireland',
    'short': 'IE',
    'shortLen3': 'IRL',
    'numberCode': '372',
    'iso': 'ISO 3166-2:IE',
    'name': '爱尔兰',
    'isTndependent': true,
    'tel': '353'
  },
  {
    'en': 'Isle of Man',
    'short': 'IM',
    'shortLen3': 'IMN',
    'numberCode': '833',
    'iso': 'ISO 3166-2:IM',
    'name': '马恩岛',
    'isTndependent': false
  },
  {
    'en': 'Israel',
    'short': 'IL',
    'shortLen3': 'ISR',
    'numberCode': '376',
    'iso': 'ISO 3166-2:IL',
    'name': '以色列',
    'isTndependent': true,
    'tel': '972'
  },
  {
    'en': 'Italy',
    'short': 'IT',
    'shortLen3': 'ITA',
    'numberCode': '380',
    'iso': 'ISO 3166-2:IT',
    'name': '意大利',
    'isTndependent': true,
    'tel': '39'
  },
  {
    'en': 'Jamaica',
    'short': 'JM',
    'shortLen3': 'JAM',
    'numberCode': '388',
    'iso': 'ISO 3166-2:JM',
    'name': '牙买加',
    'isTndependent': true,
    'tel': '1876'
  },
  {
    'en': 'Japan',
    'short': 'JP',
    'shortLen3': 'JPN',
    'numberCode': '392',
    'iso': 'ISO 3166-2:JP',
    'name': '日本',
    'isTndependent': true,
    'tel': '81'
  },
  {
    'en': 'Jersey',
    'short': 'JE',
    'shortLen3': 'JEY',
    'numberCode': '832',
    'iso': 'ISO 3166-2:JE',
    'name': '泽西',
    'isTndependent': false
  },
  {
    'en': 'Jordan',
    'short': 'JO',
    'shortLen3': 'JOR',
    'numberCode': '400',
    'iso': 'ISO 3166-2:JO',
    'name': '约旦',
    'isTndependent': true,
    'tel': '962'
  },
  {
    'en': 'Kazakhstan',
    'short': 'KZ',
    'shortLen3': 'KAZ',
    'numberCode': '398',
    'iso': 'ISO 3166-2:KZ',
    'name': '哈萨克斯坦',
    'isTndependent': true,
    'tel': '7'
  },
  {
    'en': 'Kenya',
    'short': 'KE',
    'shortLen3': 'KEN',
    'numberCode': '404',
    'iso': 'ISO 3166-2:KE',
    'name': '肯尼亚',
    'isTndependent': true,
    'tel': '254'
  },
  {
    'en': 'Kiribati',
    'short': 'KI',
    'shortLen3': 'KIR',
    'numberCode': '296',
    'iso': 'ISO 3166-2:KI',
    'name': '基里巴斯',
    'isTndependent': true,
    'tel': '686'
  },
  {
    'en': "Korea (Democratic People's Republic of)",
    'short': 'KP',
    'shortLen3': 'PRK',
    'numberCode': '408',
    'iso': 'ISO 3166-2:KP',
    'name': '朝鲜',
    'isTndependent': true,
    'tel': '850'
  },
  {
    'en': 'Korea (Republic of)',
    'short': 'KR',
    'shortLen3': 'KOR',
    'numberCode': '410',
    'iso': 'ISO 3166-2:KR',
    'name': '韩国',
    'isTndependent': true,
    'tel': '82'
  },
  {
    'en': 'Kuwait',
    'short': 'KW',
    'shortLen3': 'KWT',
    'numberCode': '414',
    'iso': 'ISO 3166-2:KW',
    'name': '科威特',
    'isTndependent': true,
    'tel': '965'
  },
  {
    'en': 'Kyrgyzstan',
    'short': 'KG',
    'shortLen3': 'KGZ',
    'numberCode': '417',
    'iso': 'ISO 3166-2:KG',
    'name': '吉尔吉斯斯坦',
    'isTndependent': true,
    'tel': '996'
  },
  {
    'en': "Lao People's Democratic Republic",
    'short': 'LA',
    'shortLen3': 'LAO',
    'numberCode': '418',
    'iso': 'ISO 3166-2:LA',
    'name': '老挝',
    'isTndependent': true,
    'tel': '856'
  },
  {
    'en': 'Latvia',
    'short': 'LV',
    'shortLen3': 'LVA',
    'numberCode': '428',
    'iso': 'ISO 3166-2:LV',
    'name': '拉脱维亚',
    'isTndependent': true,
    'tel': '371'
  },
  {
    'en': 'Lebanon',
    'short': 'LB',
    'shortLen3': 'LBN',
    'numberCode': '422',
    'iso': 'ISO 3166-2:LB',
    'name': '黎巴嫩',
    'isTndependent': true,
    'tel': '961'
  },
  {
    'en': 'Lesotho',
    'short': 'LS',
    'shortLen3': 'LSO',
    'numberCode': '426',
    'iso': 'ISO 3166-2:LS',
    'name': '莱索托',
    'isTndependent': true,
    'tel': '266'
  },
  {
    'en': 'Liberia',
    'short': 'LR',
    'shortLen3': 'LBR',
    'numberCode': '430',
    'iso': 'ISO 3166-2:LR',
    'name': '利比里亚',
    'isTndependent': true,
    'tel': '231'
  },
  {
    'en': 'Libya',
    'short': 'LY',
    'shortLen3': 'LBY',
    'numberCode': '434',
    'iso': 'ISO 3166-2:LY',
    'name': '利比亚',
    'isTndependent': true,
    'tel': '218'
  },
  {
    'en': 'Liechtenstein',
    'short': 'LI',
    'shortLen3': 'LIE',
    'numberCode': '438',
    'iso': 'ISO 3166-2:LI',
    'name': '列支敦士登',
    'isTndependent': true,
    'tel': '423'
  },
  {
    'en': 'Lithuania',
    'short': 'LT',
    'shortLen3': 'LTU',
    'numberCode': '440',
    'iso': 'ISO 3166-2:LT',
    'name': '立陶宛',
    'isTndependent': true,
    'tel': '370'
  },
  {
    'en': 'Luxembourg',
    'short': 'LU',
    'shortLen3': 'LUX',
    'numberCode': '442',
    'iso': 'ISO 3166-2:LU',
    'name': '卢森堡',
    'isTndependent': true,
    'tel': '352'
  },
  {
    'en': 'Macao',
    'short': 'MO',
    'shortLen3': 'MAC',
    'numberCode': '446',
    'iso': 'ISO 3166-2:MO',
    'name': '澳门',
    'isTndependent': false,
    'tel': '853'
  },
  {
    'en': 'Madagascar',
    'short': 'MG',
    'shortLen3': 'MDG',
    'numberCode': '450',
    'iso': 'ISO 3166-2:MG',
    'name': '马达加斯加',
    'isTndependent': true,
    'tel': '261'
  },
  {
    'en': 'Malawi',
    'short': 'MW',
    'shortLen3': 'MWI',
    'numberCode': '454',
    'iso': 'ISO 3166-2:MW',
    'name': '马拉维',
    'isTndependent': true,
    'tel': '265'
  },
  {
    'en': 'Malaysia',
    'short': 'MY',
    'shortLen3': 'MYS',
    'numberCode': '458',
    'iso': 'ISO 3166-2:MY',
    'name': '马来西亚',
    'isTndependent': true,
    'tel': '60'
  },
  {
    'en': 'Maldives',
    'short': 'MV',
    'shortLen3': 'MDV',
    'numberCode': '462',
    'iso': 'ISO 3166-2:MV',
    'name': '马尔代夫',
    'isTndependent': true,
    'tel': '960'
  },
  {
    'en': 'Mali',
    'short': 'ML',
    'shortLen3': 'MLI',
    'numberCode': '466',
    'iso': 'ISO 3166-2:ML',
    'name': '马里',
    'isTndependent': true,
    'tel': '223'
  },
  {
    'en': 'Malta',
    'short': 'MT',
    'shortLen3': 'MLT',
    'numberCode': '470',
    'iso': 'ISO 3166-2:MT',
    'name': '马耳他',
    'isTndependent': true,
    'tel': '356'
  },
  {
    'en': 'Marshall Islands',
    'short': 'MH',
    'shortLen3': 'MHL',
    'numberCode': '584',
    'iso': 'ISO 3166-2:MH',
    'name': '马绍尔群岛',
    'isTndependent': true,
    'tel': '692'
  },
  {
    'en': 'Martinique',
    'short': 'MQ',
    'shortLen3': 'MTQ',
    'numberCode': '474',
    'iso': 'ISO 3166-2:MQ',
    'name': '马提尼克',
    'isTndependent': false,
    'tel': '596'
  },
  {
    'en': 'Mauritania',
    'short': 'MR',
    'shortLen3': 'MRT',
    'numberCode': '478',
    'iso': 'ISO 3166-2:MR',
    'name': '毛里塔尼亚',
    'isTndependent': true,
    'tel': '222'
  },
  {
    'en': 'Mauritius',
    'short': 'MU',
    'shortLen3': 'MUS',
    'numberCode': '480',
    'iso': 'ISO 3166-2:MU',
    'name': '毛里求斯',
    'isTndependent': true,
    'tel': '230'
  },
  {
    'en': 'Mayotte',
    'short': 'YT',
    'shortLen3': 'MYT',
    'numberCode': '175',
    'iso': 'ISO 3166-2:YT',
    'name': '马约特',
    'isTndependent': false,
    'tel': '262'
  },
  {
    'en': 'Mexico',
    'short': 'MX',
    'shortLen3': 'MEX',
    'numberCode': '484',
    'iso': 'ISO 3166-2:MX',
    'name': '墨西哥',
    'isTndependent': true,
    'tel': '52'
  },
  {
    'en': 'Micronesia (Federated States of)',
    'short': 'FM',
    'shortLen3': 'FSM',
    'numberCode': '583',
    'iso': 'ISO 3166-2:FM',
    'name': '密克罗尼西亚联邦',
    'isTndependent': true,
    'tel': '691'
  },
  {
    'en': 'Moldova (Republic of)',
    'short': 'MD',
    'shortLen3': 'MDA',
    'numberCode': '498',
    'iso': 'ISO 3166-2:MD',
    'name': '摩尔多瓦',
    'isTndependent': true,
    'tel': '373'
  },
  {
    'en': 'Monaco',
    'short': 'MC',
    'shortLen3': 'MCO',
    'numberCode': '492',
    'iso': 'ISO 3166-2:MC',
    'name': '摩纳哥',
    'isTndependent': true,
    'tel': '377'
  },
  {
    'en': 'Mongolia',
    'short': 'MN',
    'shortLen3': 'MNG',
    'numberCode': '496',
    'iso': 'ISO 3166-2:MN',
    'name': '蒙古',
    'isTndependent': true,
    'tel': '976'
  },
  {
    'en': 'Montenegro',
    'short': 'ME',
    'shortLen3': 'MNE',
    'numberCode': '499',
    'iso': 'ISO 3166-2:ME',
    'name': '黑山',
    'isTndependent': true,
    'tel': '382'
  },
  {
    'en': 'Montserrat',
    'short': 'MS',
    'shortLen3': 'MSR',
    'numberCode': '500',
    'iso': 'ISO 3166-2:MS',
    'name': '蒙特塞拉特',
    'isTndependent': false,
    'tel': '1664'
  },
  {
    'en': 'Morocco',
    'short': 'MA',
    'shortLen3': 'MAR',
    'numberCode': '504',
    'iso': 'ISO 3166-2:MA',
    'name': '摩洛哥',
    'isTndependent': true,
    'tel': '212'
  },
  {
    'en': 'Mozambique',
    'short': 'MZ',
    'shortLen3': 'MOZ',
    'numberCode': '508',
    'iso': 'ISO 3166-2:MZ',
    'name': '莫桑比克',
    'isTndependent': true,
    'tel': '258'
  },
  {
    'en': 'Myanmar',
    'short': 'MM',
    'shortLen3': 'MMR',
    'numberCode': '104',
    'iso': 'ISO 3166-2:MM',
    'name': '缅甸',
    'isTndependent': true,
    'tel': '95'
  },
  {
    'en': 'Namibia',
    'short': 'NA',
    'shortLen3': 'NAM',
    'numberCode': '516',
    'iso': 'ISO 3166-2:NA',
    'name': '纳米比亚',
    'isTndependent': true,
    'tel': '264'
  },
  {
    'en': 'Nauru',
    'short': 'NR',
    'shortLen3': 'NRU',
    'numberCode': '520',
    'iso': 'ISO 3166-2:NR',
    'name': '瑙鲁',
    'isTndependent': true,
    'tel': '674'
  },
  {
    'en': 'Nepal',
    'short': 'NP',
    'shortLen3': 'NPL',
    'numberCode': '524',
    'iso': 'ISO 3166-2:NP',
    'name': '尼泊尔',
    'isTndependent': true,
    'tel': '977'
  },
  {
    'en': 'Netherlands',
    'short': 'NL',
    'shortLen3': 'NLD',
    'numberCode': '528',
    'iso': 'ISO 3166-2:NL',
    'name': '荷兰',
    'isTndependent': true,
    'tel': '31'
  },
  {
    'en': 'New Caledonia',
    'short': 'NC',
    'shortLen3': 'NCL',
    'numberCode': '540',
    'iso': 'ISO 3166-2:NC',
    'name': '新喀里多尼亚',
    'isTndependent': false,
    'tel': '687'
  },
  {
    'en': 'New Zealand',
    'short': 'NZ',
    'shortLen3': 'NZL',
    'numberCode': '554',
    'iso': 'ISO 3166-2:NZ',
    'name': '新西兰',
    'isTndependent': true,
    'tel': '64'
  },
  {
    'en': 'Nicaragua',
    'short': 'NI',
    'shortLen3': 'NIC',
    'numberCode': '558',
    'iso': 'ISO 3166-2:NI',
    'name': '尼加拉瓜',
    'isTndependent': true,
    'tel': '505'
  },
  {
    'en': 'Niger',
    'short': 'NE',
    'shortLen3': 'NER',
    'numberCode': '562',
    'iso': 'ISO 3166-2:NE',
    'name': '尼日尔',
    'isTndependent': true,
    'tel': '227'
  },
  {
    'en': 'Nigeria',
    'short': 'NG',
    'shortLen3': 'NGA',
    'numberCode': '566',
    'iso': 'ISO 3166-2:NG',
    'name': '尼日利亚',
    'isTndependent': true,
    'tel': '234'
  },
  {
    'en': 'Niue',
    'short': 'NU',
    'shortLen3': 'NIU',
    'numberCode': '570',
    'iso': 'ISO 3166-2:NU',
    'name': '纽埃',
    'isTndependent': false,
    'tel': '683'
  },
  {
    'en': 'Norfolk Island',
    'short': 'NF',
    'shortLen3': 'NFK',
    'numberCode': '574',
    'iso': 'ISO 3166-2:NF',
    'name': '诺福克岛',
    'isTndependent': false,
    'tel': '6723'
  },
  {
    'en': 'North Macedonia',
    'short': 'MK',
    'shortLen3': 'MKD',
    'numberCode': '807',
    'iso': 'ISO 3166-2:MK',
    'name': '北马其顿',
    'isTndependent': true
  },
  {
    'en': 'Northern Mariana Islands',
    'short': 'MP',
    'shortLen3': 'MNP',
    'numberCode': '580',
    'iso': 'ISO 3166-2:MP',
    'name': '北马里亚纳群岛',
    'isTndependent': false,
    'tel': '1670'
  },
  {
    'en': 'Norway',
    'short': 'NO',
    'shortLen3': 'NOR',
    'numberCode': '578',
    'iso': 'ISO 3166-2:NO',
    'name': '挪威',
    'isTndependent': true,
    'tel': '47'
  },
  {
    'en': 'Oman',
    'short': 'OM',
    'shortLen3': 'OMN',
    'numberCode': '512',
    'iso': 'ISO 3166-2:OM',
    'name': '阿曼',
    'isTndependent': true,
    'tel': '968'
  },
  {
    'en': 'Pakistan',
    'short': 'PK',
    'shortLen3': 'PAK',
    'numberCode': '586',
    'iso': 'ISO 3166-2:PK',
    'name': '巴基斯坦',
    'isTndependent': true,
    'tel': '92'
  },
  {
    'en': 'Palau',
    'short': 'PW',
    'shortLen3': 'PLW',
    'numberCode': '585',
    'iso': 'ISO 3166-2:PW',
    'name': '帕劳',
    'isTndependent': true,
    'tel': '680'
  },
  {
    'en': 'Palestine, State of',
    'short': 'PS',
    'shortLen3': 'PSE',
    'numberCode': '275',
    'iso': 'ISO 3166-2:PS',
    'name': '巴勒斯坦',
    'isTndependent': false,
    'tel': '970'
  },
  {
    'en': 'Panama',
    'short': 'PA',
    'shortLen3': 'PAN',
    'numberCode': '591',
    'iso': 'ISO 3166-2:PA',
    'name': '巴拿马',
    'isTndependent': true,
    'tel': '507'
  },
  {
    'en': 'Papua New Guinea',
    'short': 'PG',
    'shortLen3': 'PNG',
    'numberCode': '598',
    'iso': 'ISO 3166-2:PG',
    'name': '巴布亚新几内亚',
    'isTndependent': true,
    'tel': '675'
  },
  {
    'en': 'Paraguay',
    'short': 'PY',
    'shortLen3': 'PRY',
    'numberCode': '600',
    'iso': 'ISO 3166-2:PY',
    'name': '巴拉圭',
    'isTndependent': true,
    'tel': '595'
  },
  {
    'en': 'Peru',
    'short': 'PE',
    'shortLen3': 'PER',
    'numberCode': '604',
    'iso': 'ISO 3166-2:PE',
    'name': '秘鲁',
    'isTndependent': true,
    'tel': '51'
  },
  {
    'en': 'Philippines',
    'short': 'PH',
    'shortLen3': 'PHL',
    'numberCode': '608',
    'iso': 'ISO 3166-2:PH',
    'name': '菲律宾',
    'isTndependent': true,
    'tel': '63'
  },
  {
    'en': 'Pitcairn',
    'short': 'PN',
    'shortLen3': 'PCN',
    'numberCode': '612',
    'iso': 'ISO 3166-2:PN',
    'name': '皮特凯恩群岛',
    'isTndependent': false
  },
  {
    'en': 'Poland',
    'short': 'PL',
    'shortLen3': 'POL',
    'numberCode': '616',
    'iso': 'ISO 3166-2:PL',
    'name': '波兰',
    'isTndependent': true,
    'tel': '48'
  },
  {
    'en': 'Portugal',
    'short': 'PT',
    'shortLen3': 'PRT',
    'numberCode': '620',
    'iso': 'ISO 3166-2:PT',
    'name': '葡萄牙',
    'isTndependent': true,
    'tel': '351'
  },
  {
    'en': 'Puerto Rico',
    'short': 'PR',
    'shortLen3': 'PRI',
    'numberCode': '630',
    'iso': 'ISO 3166-2:PR',
    'name': '波多黎各',
    'isTndependent': false,
    'tel': '1787'
  },
  {
    'en': 'Qatar',
    'short': 'QA',
    'shortLen3': 'QAT',
    'numberCode': '634',
    'iso': 'ISO 3166-2:QA',
    'name': '卡塔尔',
    'isTndependent': true,
    'tel': '974'
  },
  {
    'en': 'Réunion',
    'short': 'RE',
    'shortLen3': 'REU',
    'numberCode': '638',
    'iso': 'ISO 3166-2:RE',
    'name': '留尼汪',
    'isTndependent': false,
    'tel': '262'
  },
  {
    'en': 'Romania',
    'short': 'RO',
    'shortLen3': 'ROU',
    'numberCode': '642',
    'iso': 'ISO 3166-2:RO',
    'name': '罗马尼亚',
    'isTndependent': true,
    'tel': '40'
  },
  {
    'en': 'Russian Federation',
    'short': 'RU',
    'shortLen3': 'RUS',
    'numberCode': '643',
    'iso': 'ISO 3166-2:RU',
    'name': '俄罗斯',
    'isTndependent': true,
    'tel': '7'
  },
  {
    'en': 'Rwanda',
    'short': 'RW',
    'shortLen3': 'RWA',
    'numberCode': '646',
    'iso': 'ISO 3166-2:RW',
    'name': '卢旺达',
    'isTndependent': true,
    'tel': '250'
  },
  {
    'en': 'Saint Barthélemy',
    'short': 'BL',
    'shortLen3': 'BLM',
    'numberCode': '652',
    'iso': 'ISO 3166-2:BL',
    'name': '圣巴泰勒米',
    'isTndependent': false,
    'tel': '590'
  },
  {
    'en': 'Saint Helena, Ascension and Tristan da Cunha',
    'short': 'SH',
    'shortLen3': 'SHN',
    'numberCode': '654',
    'iso': 'ISO 3166-2:SH',
    'name': '圣赫勒拿、阿森松和特里斯坦-达库尼亚',
    'isTndependent': false
  },
  {
    'en': 'Saint Kitts and Nevis',
    'short': 'KN',
    'shortLen3': 'KNA',
    'numberCode': '659',
    'iso': 'ISO 3166-2:KN',
    'name': '圣基茨和尼维斯',
    'isTndependent': true,
    'tel': '1869'
  },
  {
    'en': 'Saint Lucia',
    'short': 'LC',
    'shortLen3': 'LCA',
    'numberCode': '662',
    'iso': 'ISO 3166-2:LC',
    'name': '圣卢西亚',
    'isTndependent': true,
    'tel': '1758'
  },
  {
    'en': 'Saint Martin (French part)',
    'short': 'MF',
    'shortLen3': 'MAF',
    'numberCode': '663',
    'iso': 'ISO 3166-2:MF',
    'name': '法属圣马丁',
    'isTndependent': false
  },
  {
    'en': 'Saint Pierre and Miquelon',
    'short': 'PM',
    'shortLen3': 'SPM',
    'numberCode': '666',
    'iso': 'ISO 3166-2:PM',
    'name': '圣皮埃尔和密克隆',
    'isTndependent': false,
    'tel': '508'
  },
  {
    'en': 'Saint Vincent and the Grenadines',
    'short': 'VC',
    'shortLen3': 'VCT',
    'numberCode': '670',
    'iso': 'ISO 3166-2:VC',
    'name': '圣文森特和格林纳丁斯',
    'isTndependent': true,
    'tel': '1784'
  },
  {
    'en': 'Samoa',
    'short': 'WS',
    'shortLen3': 'WSM',
    'numberCode': '882',
    'iso': 'ISO 3166-2:WS',
    'name': '萨摩亚',
    'isTndependent': true,
    'tel': '685'
  },
  {
    'en': 'San Marino',
    'short': 'SM',
    'shortLen3': 'SMR',
    'numberCode': '674',
    'iso': 'ISO 3166-2:SM',
    'name': '圣马力诺',
    'isTndependent': true,
    'tel': '378'
  },
  {
    'en': 'Sao Tome and Principe',
    'short': 'ST',
    'shortLen3': 'STP',
    'numberCode': '678',
    'iso': 'ISO 3166-2:ST',
    'name': '圣多美和普林西比',
    'isTndependent': true,
    'tel': '239'
  },
  {
    'en': 'Saudi Arabia',
    'short': 'SA',
    'shortLen3': 'SAU',
    'numberCode': '682',
    'iso': 'ISO 3166-2:SA',
    'name': '沙特阿拉伯',
    'isTndependent': true,
    'tel': '966'
  },
  {
    'en': 'Senegal',
    'short': 'SN',
    'shortLen3': 'SEN',
    'numberCode': '686',
    'iso': 'ISO 3166-2:SN',
    'name': '塞内加尔',
    'isTndependent': true,
    'tel': '221'
  },
  {
    'en': 'Serbia',
    'short': 'RS',
    'shortLen3': 'SRB',
    'numberCode': '688',
    'iso': 'ISO 3166-2:RS',
    'name': '塞尔维亚',
    'isTndependent': true,
    'tel': '381'
  },
  {
    'en': 'Seychelles',
    'short': 'SC',
    'shortLen3': 'SYC',
    'numberCode': '690',
    'iso': 'ISO 3166-2:SC',
    'name': '塞舌尔',
    'isTndependent': true,
    'tel': '248'
  },
  {
    'en': 'Sierra Leone',
    'short': 'SL',
    'shortLen3': 'SLE',
    'numberCode': '694',
    'iso': 'ISO 3166-2:SL',
    'name': '塞拉利昂',
    'isTndependent': true,
    'tel': '232'
  },
  {
    'en': 'Singapore',
    'short': 'SG',
    'shortLen3': 'SGP',
    'numberCode': '702',
    'iso': 'ISO 3166-2:SG',
    'name': '新加坡',
    'isTndependent': true,
    'tel': '65'
  },
  {
    'en': 'Sint Maarten (Dutch part)',
    'short': 'SX',
    'shortLen3': 'SXM',
    'numberCode': '534',
    'iso': 'ISO 3166-2:SX',
    'name': '荷属圣马丁',
    'isTndependent': false,
    'tel': '1721'
  },
  {
    'en': 'Slovakia',
    'short': 'SK',
    'shortLen3': 'SVK',
    'numberCode': '703',
    'iso': 'ISO 3166-2:SK',
    'name': '斯洛伐克',
    'isTndependent': true,
    'tel': '421'
  },
  {
    'en': 'Slovenia',
    'short': 'SI',
    'shortLen3': 'SVN',
    'numberCode': '705',
    'iso': 'ISO 3166-2:SI',
    'name': '斯洛文尼亚',
    'isTndependent': true,
    'tel': '386'
  },
  {
    'en': 'Solomon Islands',
    'short': 'SB',
    'shortLen3': 'SLB',
    'numberCode': '090',
    'iso': 'ISO 3166-2:SB',
    'name': '所罗门群岛',
    'isTndependent': true,
    'tel': '677'
  },
  {
    'en': 'Somalia',
    'short': 'SO',
    'shortLen3': 'SOM',
    'numberCode': '706',
    'iso': 'ISO 3166-2:SO',
    'name': '索马里',
    'isTndependent': true,
    'tel': '252'
  },
  {
    'en': 'South Africa',
    'short': 'ZA',
    'shortLen3': 'ZAF',
    'numberCode': '710',
    'iso': 'ISO 3166-2:ZA',
    'name': '南非',
    'isTndependent': true,
    'tel': '27'
  },
  {
    'en': 'South Georgia and the South Sandwich Islands',
    'short': 'GS',
    'shortLen3': 'SGS',
    'numberCode': '239',
    'iso': 'ISO 3166-2:GS',
    'name': '南乔治亚和南桑威奇群岛',
    'isTndependent': false
  },
  {
    'en': 'South Sudan',
    'short': 'SS',
    'shortLen3': 'SSD',
    'numberCode': '728',
    'iso': 'ISO 3166-2:SS',
    'name': '南苏丹',
    'isTndependent': true,
    'tel': '211'
  },
  {
    'en': 'Spain',
    'short': 'ES',
    'shortLen3': 'ESP',
    'numberCode': '724',
    'iso': 'ISO 3166-2:ES',
    'name': '西班牙',
    'isTndependent': true,
    'tel': '34'
  },
  {
    'en': 'Sri Lanka',
    'short': 'LK',
    'shortLen3': 'LKA',
    'numberCode': '144',
    'iso': 'ISO 3166-2:LK',
    'name': '斯里兰卡',
    'isTndependent': true,
    'tel': '94'
  },
  {
    'en': 'Sudan',
    'short': 'SD',
    'shortLen3': 'SDN',
    'numberCode': '729',
    'iso': 'ISO 3166-2:SD',
    'name': '苏丹',
    'isTndependent': true,
    'tel': '249'
  },
  {
    'en': 'Suriname',
    'short': 'SR',
    'shortLen3': 'SUR',
    'numberCode': '740',
    'iso': 'ISO 3166-2:SR',
    'name': '苏里南',
    'isTndependent': true,
    'tel': '597'
  },
  {
    'en': 'Svalbard and Jan Mayen',
    'short': 'SJ',
    'shortLen3': 'SJM',
    'numberCode': '744',
    'iso': 'ISO 3166-2:SJ',
    'name': '斯瓦尔巴和扬马延',
    'isTndependent': false
  },
  {
    'en': 'Sweden',
    'short': 'SE',
    'shortLen3': 'SWE',
    'numberCode': '752',
    'iso': 'ISO 3166-2:SE',
    'name': '瑞典',
    'isTndependent': true,
    'tel': '46'
  },
  {
    'en': 'Switzerland',
    'short': 'CH',
    'shortLen3': 'CHE',
    'numberCode': '756',
    'iso': 'ISO 3166-2:CH',
    'name': '瑞士',
    'isTndependent': true,
    'tel': '41'
  },
  {
    'en': 'Syrian Arab Republic',
    'short': 'SY',
    'shortLen3': 'SYR',
    'numberCode': '760',
    'iso': 'ISO 3166-2:SY',
    'name': '叙利亚',
    'isTndependent': true,
    'tel': '963'
  },
  {
    'en': 'Taiwan, Province of China[note 1]',
    'short': 'TW',
    'shortLen3': 'TWN',
    'numberCode': '158',
    'iso': 'ISO 3166-2:TW',
    'name': '中国台湾省',
    'isTndependent': false,
    'tel': '886'
  },
  {
    'en': 'Tajikistan',
    'short': 'TJ',
    'shortLen3': 'TJK',
    'numberCode': '762',
    'iso': 'ISO 3166-2:TJ',
    'name': '塔吉克斯坦',
    'isTndependent': true,
    'tel': '992'
  },
  {
    'en': 'Tanzania, United Republic of',
    'short': 'TZ',
    'shortLen3': 'TZA',
    'numberCode': '834',
    'iso': 'ISO 3166-2:TZ',
    'name': '坦桑尼亚',
    'isTndependent': true,
    'tel': '255'
  },
  {
    'en': 'Thailand',
    'short': 'TH',
    'shortLen3': 'THA',
    'numberCode': '764',
    'iso': 'ISO 3166-2:TH',
    'name': '泰国',
    'isTndependent': true,
    'tel': '66'
  },
  {
    'en': 'Timor-Leste',
    'short': 'TL',
    'shortLen3': 'TLS',
    'numberCode': '626',
    'iso': 'ISO 3166-2:TL',
    'name': '东帝汶',
    'isTndependent': true,
    'tel': '670'
  },
  {
    'en': 'Togo',
    'short': 'TG',
    'shortLen3': 'TGO',
    'numberCode': '768',
    'iso': 'ISO 3166-2:TG',
    'name': '多哥',
    'isTndependent': true,
    'tel': '228'
  },
  {
    'en': 'Tokelau',
    'short': 'TK',
    'shortLen3': 'TKL',
    'numberCode': '772',
    'iso': 'ISO 3166-2:TK',
    'name': '托克劳',
    'isTndependent': false,
    'tel': '690'
  },
  {
    'en': 'Tonga',
    'short': 'TO',
    'shortLen3': 'TON',
    'numberCode': '776',
    'iso': 'ISO 3166-2:TO',
    'name': '汤加',
    'isTndependent': true,
    'tel': '676'
  },
  {
    'en': 'Trinidad and Tobago',
    'short': 'TT',
    'shortLen3': 'TTO',
    'numberCode': '780',
    'iso': 'ISO 3166-2:TT',
    'name': '特立尼达和多巴哥',
    'isTndependent': true,
    'tel': '1868'
  },
  {
    'en': 'Tunisia',
    'short': 'TN',
    'shortLen3': 'TUN',
    'numberCode': '788',
    'iso': 'ISO 3166-2:TN',
    'name': '突尼斯',
    'isTndependent': true,
    'tel': '216'
  },
  {
    'en': 'Türkiye',
    'short': 'TR',
    'shortLen3': 'TUR',
    'numberCode': '792',
    'iso': 'ISO 3166-2:TR',
    'name': '土耳其',
    'isTndependent': true,
    'tel': '90'
  },
  {
    'en': 'Turkmenistan',
    'short': 'TM',
    'shortLen3': 'TKM',
    'numberCode': '795',
    'iso': 'ISO 3166-2:TM',
    'name': '土库曼斯坦',
    'isTndependent': true,
    'tel': '993'
  },
  {
    'en': 'Turks and Caicos Islands',
    'short': 'TC',
    'shortLen3': 'TCA',
    'numberCode': '796',
    'iso': 'ISO 3166-2:TC',
    'name': '特克斯和凯科斯群岛',
    'isTndependent': false,
    'tel': '1649'
  },
  {
    'en': 'Tuvalu',
    'short': 'TV',
    'shortLen3': 'TUV',
    'numberCode': '798',
    'iso': 'ISO 3166-2:TV',
    'name': '图瓦卢',
    'isTndependent': true,
    'tel': '688'
  },
  {
    'en': 'Uganda',
    'short': 'UG',
    'shortLen3': 'UGA',
    'numberCode': '800',
    'iso': 'ISO 3166-2:UG',
    'name': '乌干达',
    'isTndependent': true,
    'tel': '256'
  },
  {
    'en': 'Ukraine',
    'short': 'UA',
    'shortLen3': 'UKR',
    'numberCode': '804',
    'iso': 'ISO 3166-2:UA',
    'name': '乌克兰',
    'isTndependent': true,
    'tel': '380'
  },
  {
    'en': 'United Arab Emirates',
    'short': 'AE',
    'shortLen3': 'ARE',
    'numberCode': '784',
    'iso': 'ISO 3166-2:AE',
    'name': '阿联酋',
    'isTndependent': true,
    'tel': '971'
  },
  {
    'en': 'United Kingdom of Great Britain and Northern Ireland',
    'short': 'GB',
    'shortLen3': 'GBR',
    'numberCode': '826',
    'iso': 'ISO 3166-2:GB',
    'name': '英国',
    'isTndependent': true,
    'tel': '44'
  },
  {
    'en': 'United States of America',
    'short': 'US',
    'shortLen3': 'USA',
    'numberCode': '840',
    'iso': 'ISO 3166-2:US',
    'name': '美国',
    'isTndependent': true,
    'tel': '1'
  },
  {
    'en': 'United States Minor Outlying Islands',
    'short': 'UM',
    'shortLen3': 'UMI',
    'numberCode': '581',
    'iso': 'ISO 3166-2:UM',
    'name': '美国本土外小岛屿',
    'isTndependent': false
  },
  {
    'en': 'Uruguay',
    'short': 'UY',
    'shortLen3': 'URY',
    'numberCode': '858',
    'iso': 'ISO 3166-2:UY',
    'name': '乌拉圭',
    'isTndependent': true,
    'tel': '598'
  },
  {
    'en': 'Uzbekistan',
    'short': 'UZ',
    'shortLen3': 'UZB',
    'numberCode': '860',
    'iso': 'ISO 3166-2:UZ',
    'name': '乌兹别克斯坦',
    'isTndependent': true,
    'tel': '998'
  },
  {
    'en': 'Vanuatu',
    'short': 'VU',
    'shortLen3': 'VUT',
    'numberCode': '548',
    'iso': 'ISO 3166-2:VU',
    'name': '瓦努阿图',
    'isTndependent': true,
    'tel': '678'
  },
  {
    'en': 'Venezuela (Bolivarian Republic of)',
    'short': 'VE',
    'shortLen3': 'VEN',
    'numberCode': '862',
    'iso': 'ISO 3166-2:VE',
    'name': '委内瑞拉',
    'isTndependent': true,
    'tel': '58'
  },
  {
    'en': 'Viet Nam',
    'short': 'VN',
    'shortLen3': 'VNM',
    'numberCode': '704',
    'iso': 'ISO 3166-2:VN',
    'name': '越南',
    'isTndependent': true,
    'tel': '84'
  },
  {
    'en': 'Virgin Islands (British)',
    'short': 'VG',
    'shortLen3': 'VGB',
    'numberCode': '092',
    'iso': 'ISO 3166-2:VG',
    'name': '英属维尔京群岛',
    'isTndependent': false,
    'tel': '1284'
  },
  {
    'en': 'Virgin Islands (U.S.)',
    'short': 'VI',
    'shortLen3': 'VIR',
    'numberCode': '850',
    'iso': 'ISO 3166-2:VI',
    'name': '美属维尔京群岛',
    'isTndependent': false,
    'tel': '1340'
  },
  {
    'en': 'Wallis and Futuna',
    'short': 'WF',
    'shortLen3': 'WLF',
    'numberCode': '876',
    'iso': 'ISO 3166-2:WF',
    'name': '瓦利斯和富图纳',
    'isTndependent': false,
    'tel': '681'
  },
  {
    'en': 'Western Sahara',
    'short': 'EH',
    'shortLen3': 'ESH',
    'numberCode': '732',
    'iso': 'ISO 3166-2:EH',
    'name': '西撒哈拉',
    'isTndependent': false
  },
  {
    'en': 'Yemen',
    'short': 'YE',
    'shortLen3': 'YEM',
    'numberCode': '887',
    'iso': 'ISO 3166-2:YE',
    'name': '也门',
    'isTndependent': true,
    'tel': '967'
  },
  {
    'en': 'Zambia',
    'short': 'ZM',
    'shortLen3': 'ZMB',
    'numberCode': '894',
    'iso': 'ISO 3166-2:ZM',
    'name': '赞比亚',
    'isTndependent': true,
    'tel': '260'
  },
  {
    'en': 'Zimbabwe',
    'short': 'ZW',
    'shortLen3': 'ZWE',
    'numberCode': '716',
    'iso': 'ISO 3166-2:ZW',
    'name': '津巴布韦',
    'isTndependent': true,
    'tel': '263'
  }
]
