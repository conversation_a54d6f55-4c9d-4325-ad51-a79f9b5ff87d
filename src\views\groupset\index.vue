<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="id" style="width: 80px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.subject" placeholder="标题查询" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.owner" placeholder="创建人查询" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="100">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="标题">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.subject, $event)">{{ row.subject }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建人">
        <template slot-scope="{row}">
          <el-button type="success" size="mini">{{ row.owner ? row.owner.nickname: row.user_id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" width="200">
        <template slot-scope="{row}">
          <span>{{ row.creation_ts| formatTime }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="updated_at" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row.groups)">群落成员列表</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      title="群落成员列表"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-table
        v-loading="listLoading"
        :data="groupsetAttendees"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
      >
        <el-table-column align="center" label="群ID" width="100">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="onClipboard(row.id, $event)">{{ row.id }}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="群标题">
          <template slot-scope="{row}">
            {{ row.subject }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="群主id">
          <template slot-scope="{row}">
            {{ row.admin_id }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" width="200">
          <template slot-scope="{row}">
            <span>{{ row.creation_ts| formatTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="跳转" width="100">
          <template slot-scope="{row}">
            <el-button type="success" size="mini" @click="redirect(row.id)">跳转</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getList } from '@/api/groupset'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'

export default {
  name: 'Group',
  components: { Pagination },

  filters: {
    userTypeFilter(type) {
      const typeMap = {
        1: '普通',
        2: '内部',
        3: '迈动'
      }
      return typeMap[type]
    },
    internalFilter(val) {
      return val ? '是' : '否'
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        id: null,
        subject: null,
        owner: null
      },
      dialogVisible: false,
      listLoading: true,
      groupsetAttendees: []
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      getList(this.listQuery).then(response => {
        this.list = Object.assign([], response.data.list)
        this.total = response.data.count
        this.listLoading = false
      })
    },
    async showDialog(groups) {
      this.groupsetAttendees = groups
      this.dialogVisible = true
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    },
    redirect(group_id) {
      this.$router.push({ path: 'group', query: { id: group_id }})
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
