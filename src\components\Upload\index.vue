<template>
  <div>
    <el-tooltip content="同名文件会覆盖!" placement="top-start" effect="dark">
      <el-upload
        ref="upload"
        class="upload-demo"
        action="#"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :on-change="handleChange"
        :http-request="uploadToOss"
      >
        <el-button v-show="!objKey.includes('anti_theft')" slot="trigger" size="mini" type="primary" style="margin: 0 10px;">选取文件</el-button>
        <el-button v-show="!objKey.includes('anti_theft')" v-loading="buttonLoading" type="success" size="mini" @click="submitUpload()">上传到OSS</el-button>
        <el-button size="mini" @click="onClipboard(downloadUrl, $event)">复制下载地址</el-button>
      </el-upload>
    </el-tooltip>
  </div>
</template>

<script>
import OSS from 'ali-oss' // 引入Aliyun OSS SDK
import SparkMD5 from 'spark-md5' // 用于计算MD5
import clip from '@/utils/clipboard'
const { getOssStsToken } = require('@/api/common')
const pathMap = {
  pc: 'PC',
  pc_anti_theft: 'PC',
  android: 'Android',
  androidGooglePlay: 'Android',
  ulinker: 'Android',
  teair: 'TE_Air'
}
export default {
  name: 'Upload',
  props: {
    objKey: {
      required: true,
      type: String
    },
    downloadUrl: {
      required: true,
      type: String
    }
  },
  data() {
    return {
      buttonLoading: false,
      fileSize: 0,
      md5: '',
      ossClient: null, // OSS 客户端实例
      uploadPercentage: 0, // 上传进度
      fileList: [] // 存储选择的文件
    }
  },
  methods: {
    // 在选择文件之前设置 OSS 客户端
    async beforeUpload(file) {
      this.fileSize = file.size // 获取文件大小
      this.md5 = await this.calculateFileMD5(file) // 计算文件 MD5
      // 配置 OSS 客户端, 获取临时凭证的方式依赖于你项目的实际情况
      console.log(this.fileSize, this.md5)
      return true // 允许上传
    },

    // 处理文件选择
    handleChange(file, fileList) {
      this.fileList = fileList // 保存选中的文件列表
    },

    // 自定义上传请求
    async uploadToOss({ file, onProgress }) {
      try {
        this.buttonLoading = true
        const fileName = `/Installer/${pathMap[this.objKey]}/${file.name}`
        const res = await getOssStsToken(fileName)
        if (this.objKey === 'pc') {
          const downloadOssClient = new OSS({
            endpoint: res.data.endpoint,
            accessKeyId: res.data.accessKeyId,
            accessKeySecret: res.data.accessKeySecret,
            bucket: res.data.download_bucket,
            stsToken: res.data.stsToken,
            xhrTimeout: 60000
          })
          downloadOssClient.multipartUpload(fileName, file).then(() => {
            this.$emit('onUpload', { fileSize: this.fileSize, downloadUrl: `https://rmtus-download-oss.mindray.com${fileName}`, md5: this.md5, key: 'pc_anti_theft' })
            this.$message({
              type: 'warning',
              message: '防盗链链接上传成功'
            })
          })
        }
        this.ossClient = new OSS({
          endpoint: res.data.endpoint,
          accessKeyId: res.data.accessKeyId,
          accessKeySecret: res.data.accessKeySecret,
          bucket: res.data.bucket,
          stsToken: res.data.stsToken,
          xhrTimeout: 60000
        })
        await this.ossClient.multipartUpload(fileName, file, {
          progress: (p) => {
            const percent = Math.floor(p * 100)
            this.uploadPercentage = percent
            onProgress({ percent })
          }
        })
        this.$emit('onUpload', { fileSize: this.fileSize, downloadUrl: `https://${res.data.bucket}.oss-cn-shanghai.aliyuncs.com${fileName}`, md5: this.md5, key: this.objKey })
        this.$message({
          type: 'success',
          message: '上传成功'
        })
        this.buttonLoading = false
      } catch (error) {
        console.error('文件上传失败：', error)
      }
    },

    // 手动触发上传
    submitUpload() {
      this.$refs.upload.submit()
    },

    // 计算文件的 MD5 值
    calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        const spark = new SparkMD5.ArrayBuffer()

        reader.onload = (e) => {
          spark.append(e.target.result) // 加入到 spark 计算
          const md5 = spark.end() // 获取最终的MD5值
          resolve(md5)
        }

        reader.onerror = (error) => {
          reject(error)
        }

        reader.readAsArrayBuffer(file) // 读取文件
      })
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof text === 'number') {
        text = `${text}`
      }
      clip(text, event) // 只能复制字符串
    }
  }
}
</script>

<style scoped>
.el-upload__tip {
  color: #999;
}
</style>
