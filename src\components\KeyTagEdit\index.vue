<template>
  <div @submit.native.prevent>
    <el-tag
      v-for="tag in dynamicTags"
      :key="tag"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
      @submit.native.prevent
    >
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="saveTagInput"
      v-model="inputValue"
      class="input-new-tag"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
      @submit.native.prevent
    />
    <el-button
      v-else
      class="button-new-tag"
      size="small"
      @click="showInput"
      @submit.native.prevent
    >
      + 新增
    </el-button>
    <el-button v-if="isDebugging" @click="debug" @submit.native.prevent>debugger</el-button>
  </div>
</template>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>

<script>
export default {
  name: 'KeyTagEdit',
  props: {
    dynamicTagsProps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isDebugging: false,
      inputVisible: false,
      inputValue: '',
      dynamicTags: []
    }
  },
  watch: {
    dynamicTags: function(val) {
      this.$emit('update', this.dynamicTags)
    }
  },
  created() {
    this.dynamicTags = JSON.parse(JSON.stringify(this.dynamicTagsProps))
  },
  methods: {
    debug() {
      console.log(this.dynamicTags)
      console.log(this.dynamicTagsProps)
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.inputValue
      // this.dynamicTags.findIndex(inputValue) === -1
      if (inputValue) {
        this.dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>
