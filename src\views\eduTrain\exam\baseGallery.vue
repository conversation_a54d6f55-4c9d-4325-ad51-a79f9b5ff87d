<template>
  <div>
    <el-dialog
      v-loading="loading"
      :close-on-click-modal="false"
      :visible.sync="isShowGallery"
      width="90%"
      append-to-body
      custom-class="enhanced_gallery_dialog"
      :before-close="closeGallery">
      <div ref="leftGallery" class="gallery-container">
        <!-- 关闭按钮 - 始终显示 -->
        <button class="close-btn" @click="closeGallery">
          <i class="el-icon-close" />
        </button>

        <!-- 主要查看区域 -->
        <div id="gallery-box" class="gallery-top" @mousemove="showNavArrows" @mouseleave="hideNavArrows">
          <!-- 图片/视频标题显示区域 -->
          <div class="file-info-bar" :class="{ 'hidden': !showFileInfo }">
            <div class="file-title">
              <span v-if="currentFile.label" v-html="currentFile.label" />
              <span v-else>{{ currentSliderIndex + 1 }} / {{ fileList.length }}</span>
            </div>
            <div class="file-actions">
              <button class="action-btn" @click="toggleFileInfo">
                <i class="el-icon-info" />
              </button>
            </div>
          </div>

          <!-- 标签切换按钮 - 当标签隐藏时显示 -->
          <button v-if="!showFileInfo" class="toggle-info-btn" @click="toggleFileInfo">
            <i class="el-icon-info" />
          </button>

          <!-- PDF阅读器 -->
          <!-- <pdf-reader class="pdfReader" :url="currentFile.url" :show.sync="isPdfVisible"
                        v-if="isPdfVisible"></pdf-reader> -->

          <!-- 主要内容区域 -->
          <div class="main-content-area">
            <!-- 左右导航箭头 -->
            <button
              class="gallery-nav-button prev"
              :class="{ 'visible': showArrows, 'disabled': currentSliderIndex <= 0 }"
              :disabled="currentSliderIndex <= 0"
              @click="prevImage"
            >
              <i class="el-icon-arrow-left" />
            </button>

            <button
              class="gallery-nav-button next"
              :class="{ 'visible': showArrows, 'disabled': currentSliderIndex >= fileList.length - 1 }"
              :disabled="currentSliderIndex >= fileList.length - 1"
              @click="nextImage">
              <i class="el-icon-arrow-right" />
            </button>

            <!-- 图片显示 -->
            <template v-if="currentFile.fileType === 'image'">
              <div
                class="image-container"
                @wheel.prevent="handleImageWheel($event)"
                @mousedown="startImageDrag($event)"
                @mousemove="moveImage($event)"
                @mouseup="endImageDrag"
                @mouseleave="endImageDrag"
                @dblclick="handleDoubleClick"
              >
                <img
                  ref="previewImage"
                  :src="currentFile.url"
                  class="preview"
                  draggable="false"
                  :style="imageTransformStyle"
                  @error="handleImageError(currentFile)"
                  @load="onImageLoaded"
                >
                <button
                  v-if="imageScale !== 1 || imageOffsetX !== 0 || imageOffsetY !== 0"
                  class="reset-zoom-btn"
                  @click="resetImageTransform"
                >
                  <i class="el-icon-refresh-right" />
                </button>
              </div>
            </template>

            <!-- 视频显示 -->
            <template v-else-if="currentFile.fileType === 'video'">
              <div class="video-container">
                <video
                  class="main_video"
                  :src="currentFile.url"
                  controls
                  @error="handleVideoError(currentFile)"
                />
              </div>
            </template>

            <!-- PDF显示 - 在主区域只显示占位图 -->
            <!-- <template v-else-if="currentFile.fileType === 'pdf'">
                            <div class="pdf-container">
                                <img src="static/resource_pc/images/poster2.jpg" class="preview" draggable="false" />
                                <div class="pdf-overlay">
                                    <i class="el-icon-document"></i>
                                    <span>PDF文档</span>
                                </div>
                            </div>
                        </template> -->
          </div>
        </div>

        <!-- 缩略图区域 -->
        <div class="thumb_wrap">
          <div v-show="currentSliderIndex === -1" v-loading="true" class="thumb_loading" />
          <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
            <vue-slide :key="slideKey" ref="thumb_slide" class="thumb_slide" :ops="ops">
              <div class="thumbnails-container" @mousewheel.prevent.stop>
                <div
                  v-for="(file, index) in fileList"
                  :key="index + '_' + file.url"
                  class="thumb_item"
                  :class="{ current_thumb: index === currentSliderIndex }"
                  @mousedown="mousedownThumb($event, index)"
                  @mouseup="mouseupThumb($event, index)"
                >
                  <!-- 图片缩略图 -->
                  <template v-if="file.fileType === 'image'">
                    <img
                      :src="file.url"
                      class="preview"
                      draggable="false"
                      @error="handleImageError(file)"
                    >
                    <div class="file-type-indicator">
                      <i class="el-icon-picture" />
                    </div>
                  </template>

                  <!-- 视频缩略图 -->
                  <template v-else-if="file.fileType === 'video'">
                    <div class="video-thumbnail-placeholder">
                      <i class="el-icon-video-camera video-icon" />
                      <!-- <span class="video-label">视频</span> -->
                    </div>
                  </template>

                  <!-- PDF缩略图 -->
                  <!-- <template v-else-if="file.fileType === 'pdf'">
                                        <img src="static/resource_pc/images/poster2.jpg" class="preview"
                                            draggable="false" />
                                        <div class="file-type-indicator pdf">
                                            <i class="el-icon-document"></i>
                                        </div>
                                    </template> -->
                </div>
              </div>
            </vue-slide>
          </div>
          <button class="nav-button prev" @click="lastPage">
            <i class="el-icon-arrow-left" />
          </button>
          <button class="nav-button next" @click="nextPage">
            <i class="el-icon-arrow-right" />
          </button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import vueSlide from 'vuescroll'

export default {
  name: 'BaseGallery',
  components: { vueSlide }, //, pdfReader: () => import(/* webpackPrefetch: true */ './pdfReader')
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShowGallery: false,
      currentSliderIndex: -1,
      mousedownThumpPoint: null,
      fileList: [],
      currentFile: {},
      // isPdfVisible: false,
      slideKey: 0,
      showFileInfo: true,
      showArrows: false,
      arrowsTimeout: null,
      ops: {
        vuescroll: {
          mode: 'slide',
          sizeStrategy: 'percent',
          detectResize: true,
          locking: true
        },
        scrollPanel: {
          scrollingY: false
        }
      },
      imageScale: 1,
      imageOffsetX: 0,
      imageOffsetY: 0,
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      lastOffsetX: 0,
      lastOffsetY: 0,
      containerBounds: null,
      imageBounds: null,
      maxZoomScale: 3,
      doubleClickZoomScale: 2
    }
  },
  computed: {
    imageTransformStyle() {
      return {
        transform: `scale(${this.imageScale}) translate(${this.imageOffsetX}px, ${this.imageOffsetY}px)`,
        transition: this.isDragging ? 'none' : 'transform 0.2s ease',
        transformOrigin: 'center center'
      }
    }
  },
  watch: {
    currentSliderIndex(newIndex) {
      if (newIndex >= 0 && this.fileList[newIndex]) {
        this.currentFile = this.fileList[newIndex]
        // this.handleFileTypeChange();
        this.slideThumb(newIndex)
      }
      // Reset image transform when changing slides
      this.resetImageTransform()
    },
    fileList: {
      handler(newFileList) {
        // 如果文件列表为空，重置所有状态
        if (!newFileList || newFileList.length === 0) {
          this.currentSliderIndex = -1
          this.currentFile = {}
          return
        }

        // 如果当前索引超出新列表范围，重置为第一张
        if (this.currentSliderIndex >= newFileList.length) {
          this.currentSliderIndex = 0
        }

        // 如果当前索引有效，更新当前文件
        if (this.currentSliderIndex >= 0) {
          this.currentFile = newFileList[this.currentSliderIndex]
          // this.handleFileTypeChange();
        }

        // 强制更新 slideKey 以刷新缩略图
        this.slideKey = Date.now()
      },
      deep: true
    }
  },
  mounted() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.closeGallery()
      }
    })
  },
  beforeDestroy() {
    this.closeGallery()
    if (this.arrowsTimeout) {
      clearTimeout(this.arrowsTimeout)
    }
  },
  methods: {
    // 打开画廊
    openGallery(files, startIndex = 0) {
      this.isShowGallery = true
      // this.isPdfVisible = false;
      this.slideKey = Date.now()
      this.fileList = Array.isArray(files) ? [...files] : []
      this.showFileInfo = true
      this.showArrows = false

      // 重置图片变换状态
      this.resetImageTransform()

      // 确保 startIndex 在有效范围内
      if (this.fileList.length > 0) {
        startIndex = Math.max(0, Math.min(startIndex, this.fileList.length - 1))
        this.$nextTick(() => {
          this.setCurrentIndex(startIndex)
        })
      } else {
        this.currentSliderIndex = -1
        this.currentFile = {}
      }
    },

    // 关闭画廊
    closeGallery() {
      this.resetImageTransform()
      this.isShowGallery = false
      // this.isPdfVisible = false;

      // 停止所有视频播放
      const videos = document.querySelectorAll('video')
      for (const video of videos) {
        video.pause()
      }
    },

    // 切换文件信息显示
    toggleFileInfo() {
      this.showFileInfo = !this.showFileInfo
    },

    // 设置当前索引
    setCurrentIndex(index) {
      if (index >= 0 && index < this.fileList.length) {
        this.resetImageTransform()
        this.currentSliderIndex = index
      }
    },

    // 显示导航箭头
    showNavArrows() {
      this.showArrows = true
      if (this.arrowsTimeout) {
        clearTimeout(this.arrowsTimeout)
      }
    },

    // 隐藏导航箭头
    hideNavArrows() {
      if (this.arrowsTimeout) {
        clearTimeout(this.arrowsTimeout)
      }
      this.arrowsTimeout = setTimeout(() => {
        this.showArrows = false
      }, 1000)
    },

    // 上一张图片
    prevImage() {
      if (this.currentSliderIndex > 0) {
        this.setCurrentIndex(this.currentSliderIndex - 1)
      }
    },

    // 下一张图片
    nextImage() {
      if (this.currentSliderIndex < this.fileList.length - 1) {
        this.setCurrentIndex(this.currentSliderIndex + 1)
      }
    },

    // 处理文件类型变化
    // handleFileTypeChange() {
    //     // 处理PDF文件
    //     if (this.currentFile.fileType === 'pdf') {
    //         this.isPdfVisible = true;
    //     } else {
    //         this.isPdfVisible = false;
    //     }
    // },

    // 缩略图滑动
    slideThumb(index) {
      const thumb_slide = this.$refs.thumb_slide
      const thumb_scroll_wrap = this.$refs.thumb_scroll_wrap
      if (!thumb_slide || !thumb_scroll_wrap) {
        return
      }

      const scroll_width = thumb_scroll_wrap.clientWidth
      const left = index * 157 - scroll_width / 2 + 78
      thumb_slide && thumb_slide.scrollTo({ x: left }, 100)
    },

    // 缩略图鼠标按下
    mousedownThumb(event, index) {
      this.mousedownThumpPoint = {
        x: event.x,
        y: event.y
      }
    },

    // 缩略图鼠标释放
    mouseupThumb(event, index) {
      const offsetX = this.mousedownThumpPoint.x - event.x
      const offsetY = this.mousedownThumpPoint.y - event.y

      if (Math.abs(offsetX) < 20 && Math.abs(offsetY) < 20) {
        if (index === this.currentSliderIndex) {
          return
        }
        this.setCurrentIndex(index)
      }
    },

    // 上一页
    lastPage() {
      const thumb_slide = this.$refs.thumb_slide
      if (!thumb_slide) {
        return
      }

      let left = thumb_slide.getPosition().scrollLeft
      const thumb_scroll_wrap = this.$refs.thumb_scroll_wrap
      const scroll_width = thumb_scroll_wrap.clientWidth
      left -= scroll_width
      thumb_slide && thumb_slide.scrollTo({ x: left }, 150)
    },

    // 下一页
    nextPage() {
      const thumb_slide = this.$refs.thumb_slide
      if (!thumb_slide) {
        return
      }

      let left = thumb_slide.getPosition().scrollLeft
      const thumb_scroll_wrap = this.$refs.thumb_scroll_wrap
      const scroll_width = thumb_scroll_wrap.clientWidth
      left += scroll_width
      thumb_slide && thumb_slide.scrollTo({ x: left }, 150)
    },

    // 图片加载错误处理
    handleImageError(file) {
      console.error('Image load error:', file.url)
      // 可以设置一个默认的错误图片
      // file.url = 'path/to/error-image.jpg';
    },

    // 视频加载错误处理
    handleVideoError(file) {
      console.error('Video load error:', file.url)
    },

    // 更新边界框计算方法
    updateBoundingBoxes() {
      const container = this.$el.querySelector('.image-container')
      const image = this.$el.querySelector('.image-container .preview')

      if (container && image) {
        this.containerBounds = container.getBoundingClientRect()

        // 获取图片的原始尺寸（未缩放状态）
        const naturalWidth = image.naturalWidth
        const naturalHeight = image.naturalHeight

        // 获取图片当前显示尺寸
        const rect = image.getBoundingClientRect()

        // 保存图片信息
        this.imageBounds = {
          width: rect.width / this.imageScale,
          height: rect.height / this.imageScale,
          naturalWidth,
          naturalHeight,
          ratio: naturalWidth / naturalHeight,
          containerCenterX: this.containerBounds.width / 2,
          containerCenterY: this.containerBounds.height / 2
        }
      }
    },

    // 处理双击缩放
    handleDoubleClick(event) {
      if (this.currentFile.fileType !== 'image') {
        return
      }

      // 如果已经放大，则重置为原始大小
      if (this.imageScale > 1) {
        this.resetImageTransform()
      } else {
        // 放大到预设比例
        this.imageScale = this.doubleClickZoomScale

        // 计算容器和图片尺寸以限制拖动范围
        this.$nextTick(() => {
          this.updateBoundingBoxes()
        })
      }
    },

    // 处理鼠标滚轮缩放
    handleImageWheel(event) {
      if (this.currentFile.fileType !== 'image') {
        return
      }

      // 确定缩放方向
      const delta = event.deltaY || event.detail || event.wheelDelta
      const zoomFactor = delta > 0 ? 0.9 : 1.1 // 缩小或放大

      // 计算新的缩放比例
      let newScale = this.imageScale * zoomFactor

      // 限制缩放比例在0.5到maxZoomScale之间
      newScale = Math.max(0.5, Math.min(this.maxZoomScale, newScale))

      // 应用新的缩放比例
      this.imageScale = newScale

      // 更新边界框以限制拖动范围
      this.$nextTick(() => {
        this.updateBoundingBoxes()
      })
    },

    // 开始图片拖动
    startImageDrag(event) {
      if (this.currentFile.fileType !== 'image' || this.imageScale <= 1) {
        return
      }

      this.isDragging = true
      this.dragStartX = event.clientX
      this.dragStartY = event.clientY
      this.lastOffsetX = this.imageOffsetX
      this.lastOffsetY = this.imageOffsetY

      // 更新边界框以限制拖动范围
      this.updateBoundingBoxes()

      // 改变光标样式
      event.target.style.cursor = 'grabbing'
    },

    // 拖动图片时限制在容器内
    moveImage(event) {
      if (!this.isDragging) {
        return
      }

      const dx = (event.clientX - this.dragStartX) / this.imageScale
      const dy = (event.clientY - this.dragStartY) / this.imageScale

      // 计算新的偏移量
      let newOffsetX = this.lastOffsetX + dx
      let newOffsetY = this.lastOffsetY + dy

      // 如果有有效的边界框，则应用约束
      if (this.containerBounds && this.imageBounds) {
        // 计算图片的实际尺寸（考虑缩放）
        const scaledWidth = this.imageBounds.width * this.imageScale
        const scaledHeight = this.imageBounds.height * this.imageScale

        // 计算图片超出容器的部分（每边）
        const containerWidth = this.containerBounds.width
        const containerHeight = this.containerBounds.height

        // 计算最大可移动距离（考虑缩放和居中）
        const maxMoveX = Math.max(0, (scaledWidth - containerWidth) / (2 * this.imageScale))
        const maxMoveY = Math.max(0, (scaledHeight - containerHeight) / (2 * this.imageScale))

        // 限制偏移量在允许范围内
        newOffsetX = Math.max(-maxMoveX, Math.min(maxMoveX, newOffsetX))
        newOffsetY = Math.max(-maxMoveY, Math.min(maxMoveY, newOffsetY))
      }

      // 应用受限制的偏移量
      this.imageOffsetX = newOffsetX
      this.imageOffsetY = newOffsetY
    },

    // End image drag
    endImageDrag(event) {
      if (!this.isDragging) {
        return
      }

      this.isDragging = false
      if (event && event.target) {
        event.target.style.cursor = 'grab'
      }
    },

    // Reset image to original size and position
    resetImageTransform() {
      this.imageScale = 1
      this.imageOffsetX = 0
      this.imageOffsetY = 0
      this.isDragging = false
      this.containerBounds = null
      this.imageBounds = null
    },

    // 添加onImageLoaded方法
    onImageLoaded() {
      this.$nextTick(() => {
        this.updateBoundingBoxes()
      })
    }
  }
}
</script>

<style lang="scss">
.enhanced_gallery_dialog {
    background: #1a1a1a;
    height: 90% !important;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid #333;
    border-radius: 6px;

    .el-dialog__header {
        display: none; // 隐藏原始头部，使用自定义头部
    }

    .el-dialog__body {
        position: relative;
        flex: 1;
        display: flex;
        padding: 0;
        height: 100%;
        overflow: hidden;
    }
}

.gallery-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
    height: 100%;
    position: relative;

    // 关闭按钮 - 始终显示在右上角
    .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        border: none;
        color: #fff;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background 0.2s;
        z-index: 100; // 提高z-index确保在最上层

        &:hover {
            background: rgba(255, 0, 0, 0.5);
        }
    }

    .gallery-top {
        flex: 1;
        position: relative;
        width: 100%;
        height: calc(100% - 120px);
        overflow: hidden;

        // 标签切换按钮 - 当标签隐藏时显示
        .toggle-info-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: #fff;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 10;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        }

        .file-info-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            min-height: 50px;
            max-height: 150px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 60px 10px 20px;
            z-index: 10;
            transition: opacity 0.3s ease, transform 0.3s ease;

            &.hidden {
                opacity: 0;
                transform: translateY(-100%);
                pointer-events: none;
            }

            .file-title {
                color: #fff;
                font-size: 16px;
                max-width: 80%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .file-actions {
                display: flex;
                gap: 15px;

                .action-btn {
                    background: transparent;
                    border: none;
                    color: #fff;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 4px;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                }
            }
        }

        .main-content-area {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .gallery-nav-button {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: rgba(0, 0, 0, 0.5);
                border: none;
                color: #fff;
                font-size: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                z-index: 10;
                opacity: 0;
                pointer-events: none;

                &.visible {
                    opacity: 0.7;
                    pointer-events: auto;
                }

                &:hover {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.7);
                }

                &.disabled {
                    opacity: 0.3;
                    cursor: not-allowed;
                    background: rgba(0, 0, 0, 0.3);
                }

                &.prev {
                    left: 20px;
                }

                &.next {
                    right: 20px;
                }
            }

            .image-container,
            .video-container {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .image-container {
                cursor: grab;
                overflow: hidden;
                position: relative;

                .preview {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                    border-radius: 4px;
                    transform-origin: center center;
                    will-change: transform;
                    transition: transform 0.2s ease;

                    &:active {
                        cursor: grabbing;
                    }
                }

                .reset-zoom-btn {
                    position: absolute;
                    bottom: 20px;
                    right: 20px;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: rgba(0, 0, 0, 0.5);
                    border: none;
                    color: #fff;
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background 0.2s;
                    z-index: 10;

                    &:hover {
                        background: rgba(0, 0, 0, 0.7);
                    }
                }
            }

            .video-container {
                .main_video {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                    border-radius: 4px;
                }
            }

    //         .pdf-container {
    //             position: relative;

    //             .preview {
    //                 max-width: 90%;
    //                 max-height: 90%;
    //                 object-fit: contain;
    //                 box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    //                 border-radius: 4px;
    //             }

    //             .pdf-overlay {
    //                 position: absolute;
    //                 top: 50%;
    //                 left: 50%;
    //                 transform: translate(-50%, -50%);
    //                 display: flex;
    //                 flex-direction: column;
    //                 align-items: center;
    //                 color: #fff;

    //                 i {
    //                     font-size: 48px;
    //                     margin-bottom: 10px;
    //                 }

    //                 span {
    //                     font-size: 18px;
    //                 }
    //             }
    //         }
        }
    }

    .thumb_wrap {
        height: 120px;
        padding: 10px 50px;
        position: relative;
        background: #111;
        border-top: 1px solid #333;

        .thumb_loading {
            position: absolute;
            top: 0;
            width: calc(100% - 100px);
            height: 100%;
            background-color: #111;
            z-index: 99;
        }

        .thumb_scroll_wrap {
            width: 100%;
            height: 100%;
            user-select: none;

            .thumb_slide {
                position: relative;
                width: 100%;
                height: 100%;
                z-index: 1;

                .thumbnails-container {
                    display: grid;
                    grid-auto-flow: column;
                    grid-auto-columns: 140px;
                    gap: 8px;
                    height: 100%;
                    align-items: center;
                }

                .thumb_item {
                    height: 90px;
                    background: #222;
                    position: relative;
                    cursor: pointer;
                    border-radius: 4px;
                    overflow: hidden;
                    transition: transform 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 5px 0;

                    // 重置所有可能的边框和轮廓
                    border: none;
                    outline: none;
                    box-shadow: none;

                    &:hover {
                        transform: translateY(-2px);
                    }

                    &.current_thumb {
                        position: relative;
                        border: 2px solid #4a9eff;
                    }

                    .preview {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        position: relative;
                        z-index: 1;
                        // 重置图片可能的边框和轮廓
                        border: none;
                        outline: none;
                        box-shadow: none;
                        // 确保图片填满容器
                        display: block;
                        margin: 0;
                        padding: 0;
                    }

                    .video-thumbnail-placeholder {
                        width: 100%;
                        height: 100%;
                        // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 4px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                        cursor: pointer;

                        .video-icon {
                            font-size: 24px;
                            margin-bottom: 2px;
                        }

                        // .video-label {
                        //     font-size: 10px;
                        //     font-weight: 500;
                        // }

                        // &:hover {
                        //     background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                        // }
                    }

                    .file-type-indicator {
                        position: absolute;
                        bottom: 5px;
                        right: 5px;
                        width: 24px;
                        height: 24px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                        z-index: 6;

                        &.video {
                            background: rgba(255, 0, 0, 0.6);
                        }

                        // &.pdf {
                        //     background: rgba(0, 112, 255, 0.6);
                        // }

                        i {
                            font-size: 14px;
                        }
                    }
                }

                .__bar-is-horizontal,
                .__bar-is-vertical {
                    display: none;
                }
            }
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: #fff;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 20;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            &.prev {
                left: 10px;
            }

            &.next {
                right: 10px;
            }
        }
    }
}

.pdfReader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: #fff;
}

</style>
