<template>
  <div>
    <el-dialog
      :title="computedDialogTitle"
      :visible.sync="dialogVisible"
      width="fit-content"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form v-if="form" ref="form" :model="form" class="assessment-form" :rules="rules" label-width="auto">
        <el-form-item :label="lang.dialog.config.assessmentMode" prop="mode">
          <el-select v-model="form.mode" :placeholder="lang.dialog.config.assessmentModePlaceholder" :disabled="form.project.status !== '1'"> <!-- 1: pending, 2: active, 3: inactive :disabled="form.project.status === '2'"-->
            <el-option :label="lang.options.assessmentMode.single" value="single" />
            <el-option :label="lang.options.assessmentMode.multiple" value="multiple" />
          </el-select>
        </el-form-item>
        <el-form-item :label="lang.dialog.config.deadline" prop="deadline">
          <el-date-picker
            v-model="form.deadline"
            type="date"
            :placeholder="lang.dialog.config.deadlinePlaceholder"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item class="assessment-item" :label="lang.dialog.config.attemptsAllowed" prop="attemptsAllowed">
          <el-input-number v-model="form.attemptsAllowed" :min="0" :placeholder="lang.dialog.config.attemptsAllowedPlaceholder" />
        </el-form-item>
        <el-form-item v-if="form.type === 'quiz'" class="assessment-item" :label="lang.dialog.config.minPassQuestions" prop="minPassQuestions">
          <el-input-number v-model="form.minPassQuestions" :min="0" :placeholder="lang.dialog.config.minPassQuestionsPlaceholder" />
        </el-form-item>
        <el-form-item v-if="form.type === 'quiz'" class="assessment-item" :label="lang.dialog.config.antiCheatMode" prop="antiCheatEnabled">
          <el-radio-group v-model="form.antiCheatEnabled">
            <el-radio-button :label="1">{{ lang.options.common.on }}</el-radio-button>
            <el-radio-button :label="0">{{ lang.options.common.off }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item v-if="form.type === 'quiz'" class="assessment-item">
          <el-button size="large" align="center" type="success" :disabled="form.paperInfo.length > 0" @click="handleOpenUploadDialog">{{ lang.buttons.upload }}</el-button>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ lang.buttons.cancel }}</el-button>
        <el-button type="primary" @click="handleSave">{{ lang.buttons.save }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { zh, en } from './lang.js'
import { eduService } from '@/api/education.js'

export default {
  name: 'AssessmentConfigDialog',
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    assessmentData: {
      type: Object,
      default: () => null
    },
    activeLanguage: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      form: null,
      rules: {},
      typeMap: {
        upload: 1,
        quiz: 2
      },
      modeMap: {
        single: 1,
        multiple: 2
      }
    }
  },
  computed: {
    lang() {
      return this.activeLanguage === 'zh' ? zh : en
    },
    computedDialogTitle() {
      if (this.assessmentData && this.assessmentData.name) {
        return `${this.lang.dialog.config.titlePrefix} - ${this.assessmentData.name}`
      }
      return this.lang.dialog.config.titlePrefix
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal) {
        this.initializeFormAndRules()
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate()
          }
        })
      } else {
        this.form = null
      }
    },
    assessmentData: {
      handler(newVal) {
        if (this.dialogVisible && newVal) {
          this.initializeFormAndRules()
        }
      },
      deep: true,
      immediate: true
    },
    lang() {
      if (this.dialogVisible) {
        this.initializeRules()
      }
    }
  },
  methods: {
    initializeFormAndRules() {
      if (this.assessmentData) {
        this.form = JSON.parse(JSON.stringify(this.assessmentData))
        if (this.form.attemptsAllowed === undefined) this.$set(this.form, 'attemptsAllowed', this.form.type === 'upload' ? 1 : 0)
        if (this.form.minPassQuestions === undefined) this.$set(this.form, 'minPassQuestions', 0)
        if (this.form.deadline === undefined) this.$set(this.form, 'deadline', '')
        if (this.form.mode === undefined) this.$set(this.form, 'mode', this.form.attemptsAllowed === 1 ? 'single' : 'multiple')
        if (this.form.antiCheatEnabled === undefined) this.$set(this.form, 'antiCheatEnabled', '')
      }
      this.initializeRules()
    },
    initializeRules() {
      this.rules = {
        type: [{ required: true, message: this.lang.dialog.config.assessmentModeRequired, trigger: 'change' }],
        mode: [{ required: true, message: this.lang.dialog.config.assessmentModeRequired, trigger: 'change' }],
        deadline: [{ required: true, message: this.lang.dialog.config.deadlineRequired, trigger: 'change' }],
        attemptsAllowed: [
          { required: false, message: this.lang.dialog.config.attemptsAllowedRequired, trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (value === null || value === undefined) { // || value < (this.form.type === 'upload' ? 1 : 0)
              callback(new Error(this.lang.dialog.config.attemptsAllowedRequired))
            } else if (!Number.isInteger(value)) {
              callback(new Error(this.lang.dialog.config.attemptsAllowedInvalid))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        minPassQuestions: [
          { required: true, message: this.lang.dialog.config.minPassQuestionsRequired, trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (value === null || value === undefined || value < 0) {
              callback(new Error(this.lang.dialog.config.minPassQuestionsRequired))
            } else if (!Number.isInteger(value)) {
              callback(new Error(this.lang.dialog.config.minPassQuestionsInvalid))
            } else if (this.form.questionCount && value > this.form.questionCount) {
              callback(new Error(this.lang.dialog.config.minPassQuestionsExceedsTotal))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        antiCheatEnabled: [
          { required: true, message: this.lang.dialog.config.antiCheatModeRequired, trigger: 'change' }
        ]
      }
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const requestData = {
            testID: this.form.testID,
            trainingID: this.form.project.id,
            title: this.form.name,
            deadline: this.form.deadline ? Math.floor(new Date(this.form.deadline).getTime() / 1000) : null,
            minPassQuestions: this.form.minPassQuestions,
            maxRetry: this.form.attemptsAllowed,
            retryType: this.modeMap[this.form.mode],
            type: this.typeMap[this.form.type],
            antiCheatEnabled: this.form.antiCheatEnabled
          }

          eduService.assessmentConfig(requestData)
            .then(response => {
              if (response.code === 200) {
                this.$message.success(this.lang.messages.configSuccess)
                this.$emit('save')
                this.handleClose()
              } else {
                this.$message.error(response.message || this.lang.messages.configFailed)
              }
            })
            .catch(error => {
              console.error('配置考核失败:', error)
              this.$message.error(this.lang.messages.configFailed)
            })
        } else {
          let firstError = null
          try {
            const errorFields = this.$refs.form.fields
            for (const field of errorFields) {
              if (field.validateState === 'error') {
                firstError = field.validateMessage
                break
              }
            }
          } catch (e) {
            // silent
          }
          if (firstError && firstError.trim() !== '' && firstError.trim() !== ' ') {
            // Element UI 校验信息会自动显示，此处可选择是否额外提示
            // this.$message.error(firstError)
          } else if (!firstError || firstError.trim() === ' ' || firstError.trim() === '') {
            this.$message.error(this.lang.messages.formValidationError)
          }
          return false
        }
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
.el-date-picker {
  width: 100%;
}
.el-input-number {
  width: 100%;
}
</style>
