<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.product_name" placeholder="产品型号" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.operation_system" placeholder="操作系统" clearable style="width: 150px" class="filter-item">
        <el-option key="win32" label="win32" value="win32" />
        <el-option key="linux" label="linux" value="linux" />
      </el-select>
      <el-select v-model="listQuery.regulation_type" placeholder="法规地区" clearable style="width: 150px" class="filter-item">
        <el-option key="regulation_type0" label="SFDA" value="SFDA" />
        <el-option key="regulation_type1" label="China" value="China" />
        <el-option key="regulation_type2" label="CE" value="CE" />
      </el-select>
      <el-select v-model="listQuery.force_upgrade" placeholder="是否强制升级" clearable style="width: 150px" class="filter-item">
        <el-option key="force_upgrade0" label="否" :value="false" />
        <el-option key="force_upgrade1" label="是" :value="true" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreateDialog">
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="序列号" width="95">
        <template slot-scope="{row}">
          {{ row.series_number }}
        </template>
      </el-table-column>
      <el-table-column label="法规地区" align="center">
        <template slot-scope="{row}">
          {{ row.regulation_type }}
        </template>
      </el-table-column>
      <el-table-column label="产品型号" align="center">
        <template slot-scope="{row}">
          <span>{{ row.product_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作系统" align="center">
        <template slot-scope="{row}">
          {{ row.operation_system }}
        </template>
      </el-table-column>
      <el-table-column label="upg版本" align="center">
        <template slot-scope="{row}">
          {{ row.software_version }}
        </template>
      </el-table-column>
      <el-table-column label="升级版本" align="center">
        <template slot-scope="{row}">
          {{ row.target_version }}
        </template>
      </el-table-column>
      <el-table-column label="最小支持版本号" align="center">
        <template slot-scope="{row}">
          {{ row.min_support_version }}
        </template>
      </el-table-column>
      <el-table-column label="说明" align="center">
        <template slot-scope="{row}">
          {{ row.description }}
        </template>
      </el-table-column>
      <el-table-column label="是否强制升级" align="center">
        <template slot-scope="{row}">
          {{ row.force_upgrade }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="updateDopplerUpgrade(row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="findList(listQuery)" />
    <el-dialog
      :title="dialogTitle[dialogStatus]"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-form ref="form" :model="newDopplerUpgrade" label-width="150px" :inline="false" size="normal">
        <el-row>
          <el-col :span="6">
            <el-form-item label="序列号">
              <el-input v-model="newDopplerUpgrade.series_number" placeholder="序列号" size="normal" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品型号">
              <el-input v-model="newDopplerUpgrade.product_name" placeholder="产品型号" size="normal" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="MD5">
              <el-input v-model="newDopplerUpgrade.md5" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="法规地区">
              <el-select v-model="newDopplerUpgrade.regulation_type">
                <el-option key="regulation_type0" label="SFDA" value="SFDA" />
                <el-option key="regulation_type1" label="China" value="China" />
                <el-option key="regulation_type2" label="CE" value="CE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="upg版本">
              <el-input v-model="newDopplerUpgrade.software_version" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="升级版本">
              <el-input v-model="newDopplerUpgrade.target_version" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最小支持版本号">
              <el-input v-model="newDopplerUpgrade.min_support_version" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作系统">
              <el-select v-model="newDopplerUpgrade.operation_system">
                <el-option key="win32" label="win32" value="win32" />
                <el-option key="linux" label="linux" value="linux" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="下载地址">
              <el-input v-model="newDopplerUpgrade.download_url" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="权限">
              <el-row>
                <el-col :span="8" :offset="0">
                  <el-select v-model="newDopplerUpgrade.permission.webim" placeholder="webim" style="margin-right: 10px;">
                    <el-option key="permission1" label="否" :value="false" />
                    <el-option key="permission2" label="是" :value="true" />
                  </el-select>
                </el-col>
                <el-col :span="8" :offset="0">
                  <el-select v-model="newDopplerUpgrade.permission.remote" placeholder="remote" style="margin-right: 10px;">
                    <el-option key="permission3" label="否" :value="false" />
                    <el-option key="permission4" label="是" :value="true" />
                  </el-select>
                </el-col>
                <el-col :span="8" :offset="0">
                  <el-select v-model="newDopplerUpgrade.permission.ai" placeholder="ai" style="margin-right: 10px;">
                    <el-option key="permission5" label="否" :value="false" />
                    <el-option key="permission6" label="是" :value="true" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否强制升级">
              <el-select v-model="newDopplerUpgrade.force_upgrade">
                <el-option key="force_upgrade0" label="否" :value="false" />
                <el-option key="force_upgrade1" label="是" :value="true" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-model="newDopplerUpgrade.description" :row="3" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item style="float: right; margin-right: 10px;">
            <el-button v-if="dialogStatus==='create'" type="primary" @click="handleCreate">提交</el-button>
            <el-button v-else type="primary" @click="handleUpdate">提交</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, createdDopplerUpgrade, editDopplerUpgrade } from '@/api/doppler'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'

export default {
  name: 'AgoraChannel',
  components: { Pagination },

  filters: {
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10
      },
      dialogTitle: {
        create: '新增配置',
        update: '修改配置'
      },
      dialogStatus: 'create',
      newDopplerUpgrade: { permission: {}},
      dialogVisible: false,
      listLoading: true,
      jsonData: {}
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      this.listQuery.page = 1
      this.findList()
    },
    findList() {
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof text === 'number') {
        text = `${text}`
      }
      clip(text, event) // 只能复制字符串
    },
    handleCreateDialog() {
      this.resetCreate()
      this.dialogVisible = true
      this.dialogStatus = 'create'
    },
    resetCreate() {
      this.newDopplerUpgrade = Object.assign({}, { permission: {}})
    },
    handleCreate() {
      this.$confirm('请确认此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        createdDopplerUpgrade(this.newDopplerUpgrade).then((res) => {
          if (res.code === 200) {
            this.handleFilter()
            this.$message({
              type: 'success',
              message: '成功'
            })
            this.dialogVisible = false
            this.findList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    updateDopplerUpgrade(item) {
      this.dialogStatus = 'update'
      this.newDopplerUpgrade = { ...item }
      this.dialogVisible = true
    },
    handleUpdate() {
      this.$confirm('请确认此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const { _id, ...body } = this.newDopplerUpgrade
        editDopplerUpgrade(_id, body).then((res) => {
          if (res.code === 200) {
            this.handleFilter()
            this.$message({
              type: 'success',
              message: '成功'
            })
            this.dialogVisible = false
            this.findList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    }
  }
}
</script>

<style scoped>

.editor-container{
  position: relative;
  height: 100%;
}
.table_more > .el-button {
  margin: 3px
}
</style>
