<template>
  <div class="app-container">
    <el-tabs v-model="activeName" size="medium" @tab-click="handleChangeTabs">
      <el-tab-pane label="区间表" name="statData">
        <div class="filter-container">
          <span class="filter-item">组织/用户:</span>
          <el-select
            v-model="originationId"
            v-loadMore="loadMore"
            class="filter-item"
            width="150px"
            clearable
            filterable
            remote
            :remote-method="remoteFind"
            @change="changeOrg"
            @clear="clearSelect"
          >
            <el-option
              v-for="item of originationOptions"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
          <el-select
            v-model="listQuery.userIds"
            class="filter-item"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item of userListOptions"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
          <el-date-picker
            v-model="listQuery.timeScope"
            class="filter-item"
            type="daterange"
            align="right"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        </div>
        <el-descriptions direction="horizontal" :column="1" border>
          <el-descriptions-item label="统计项">统计结果</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="实时直播总时长">{{ desc.living_duration|parseLivingDuration }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="实时直播次数">{{ desc.living_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="观看直播同时长">{{ desc.living_watch_duration|parseLivingDuration }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="聊天消息条数">{{ desc.chatMessageTypeCount|chatTypeFilter }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="检查数量">{{ desc.case_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="离线阅片发起次数">{{ desc.start_offline_consultation_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="发言条数">{{ desc.text_chat_message_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="批注条数">{{ desc.comment_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="Tags条数">{{ desc.tags_count }}</el-descriptions-item>
          <el-descriptions-item :label-style="{'width': '300px'}" label="客户端类型">{{ desc.client_type|parseClientType }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="图表" name="chart">
        <div class="filter-container">
          <span class="filter-item">组织/用户:</span>
          <el-select
            v-model="originationId"
            v-loadMore="loadMore"
            class="filter-item"
            width="150px"
            clearable
            filterable
            remote
            :remote-method="remoteFind"
            @change="changeOrg"
            @clear="clearSelect"
          >
            <el-option
              v-for="item of originationOptions"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
          <el-select
            v-model="listQuery.userIds"
            class="filter-item"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item of userListOptions"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            />
          </el-select>
          <el-date-picker
            v-model="listQuery.timeScope"
            class="filter-item"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />
          <el-select v-model="listQuery.statisItem" class="filter-item" placeholder="统计项" clearable multiple collapse-tags>
            <div v-if="originationId || listQuery.userIds.length">
              <!-- <el-option label="新增检查数" value="increaseExamCount" />
              <el-option label="新增检查数(iworks)" value="increaseExamIworksCount" />
              <el-option label="新增检查图片数" value="increaseExamImageCount" />
              <el-option label="新增检查视频数" value="increaseExamVideoCount" />
              <el-option label="新增实时直播场数" value="increaseLiveCount" />
              <el-option label="新增发言数(除系统消息外所有类型的聊天消息)" value="increaseMessageCount" />
              <el-option label="客户端类型" value="clientType" /> -->
              <el-option label="发起直播时长(分钟)" value="liveDurationCount" />
              <el-option label="接收直播时长(分钟)" value="liveAttendeeDurationCount" />
            </div>
            <div v-else>
              <el-option label="新增用户数" value="increaseUserCount" />
              <el-option label="新增用户数（审批通过）" value="increaseFormalUserCount" />
              <el-option label="累计用户数" value="totalUserCount" />
              <el-option label="活跃用户数（周期内有登录的，就记入活跃用户）" value="activeUserCount" />
              <el-option label="新增u-Link用户数" value="increaseULinkUser" />
              <el-option label="累计u-Link用户数" value="totalULinkUser" />
              <el-option label="新增群数" value="increaseGroupCount" />
              <el-option label="累计群数" value="totalGroupCount" />
              <el-option label="活跃群数（周期内有发言的，就记入活跃群）" value="activeGroupCount" />
              <!-- <el-option label="新增u-Link装机数" value="increaseULinkCount" />
              <el-option label="累计u-Link装机数" value="totalULinkCount" /> -->
            </div>
          </el-select>
          <el-select v-model="listQuery.granularity" class="filter-item" placeholder="粒度" clearable filterable>
            <el-option label="按天" value="day" />
            <el-option label="按周" value="week" />
            <el-option label="按月" value="month" />
          </el-select>

          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="drawLine">搜索</el-button>
        </div>
        <div ref="echart" style="width:100%;height:600px" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getList as getOrgList } from '@/api/organization'
import { getStaticData, getChartData, getOrgsUser } from '@/api/statistic'
export default {
  filters: {
    chatTypeFilter({ TEXT, LIVESTREAM_RECORD_VIDEO, MEDIC_IMAGE, MEDIC_VIDEO, IMAGE, VIDEO, VOICE_MESSAGE_FILE, other }) {
      return `文字: ${TEXT}/ 医疗图像: ${MEDIC_IMAGE}/ 医疗视频: ${MEDIC_VIDEO}/ 直播回放: ${LIVESTREAM_RECORD_VIDEO}/ 图像: ${IMAGE}/ 视频: ${VIDEO}/ 离线语音: ${VOICE_MESSAGE_FILE}/ 其他: ${other}`
    },
    parseLivingDuration(val) {
      if (val < 60) {
        return `${val}秒`
      } else if (val < 60 * 60) {
        return `${Math.floor(val / 60)}分钟${val % 60}秒`
      } else {
        const hour = Math.floor(val / 60 / 60)
        const min = Math.floor((val - hour * 60 * 60) / 60)
        return `${hour}小时${min}分钟${val % 60}秒`
      }
    },
    parseClientType(client_type) {
      const map = {
        AppUltraSyncBox: '盒子',
        AppWorkstation: '工作站',
        AppClient: '会诊端',
        AppMobile: '手机App',
        Doppler: '超声机器',
        Browser: '浏览器'
      }
      let str = ''
      for (const key in client_type) {
        str += `${map[key]}:${client_type[key]} /`
      }
      return str.slice(0, -1)
    }
  },
  directives: {
    loadMore: { // 自定义指令: 下拉加载
      bind(el, binding) {
        const selector = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        selector.addEventListener('scroll', function() {
          // scrollTop 这里可能因为浏览器缩放存在小数点的情况，导致了滚动到底部时
          // scrollHeight 减去滚动到底部时的scrollTop ，依然大于clientHeight 导致无法请求更多数据
          // 这里将scrollTop向上取整 保证滚到底部时，触发调用
          const CONDITION = this.scrollHeight - Math.ceil(this.scrollTop) <= this.clientHeight
          // el.scrollTop !== 0 当输入时，如果搜索结果很少，以至于没看到滚动条，那么此时的CONDITION计算结果是true，会执行bind.value()，此时不应该执行，否则搜索结果不匹配
          if (CONDITION && this.scrollTop !== 0) {
            binding.value()
          }
        })
      }
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      desc: { chatMessageTypeCount: {}, client_type: {}},
      listQuery: {
        userIds: [],
        timeScope: [Date.now(), Date.now() + 3600 * 1000 * 24],
        statisItem: [],
        granularity: 'month'
      },
      selectListQuery: {
        page: 1,
        pagesize: 8,
        name: null
      },
      scrollToEnd: false,
      originationId: '',
      userListOptions: [],
      originationOptions: [{ id: -1, label: '无所属医院用户' }],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 7
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 30
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = Date.now()
            const start = end - 3600 * 1000 * 24 * 90
            picker.$emit('pick', [start, end])
          }
        }]
      },
      activeName: 'statData',
      myChart: {}
    }
  },
  async created() {
    await this.getOrgOption()
    this.handleFilter()
  },
  methods: {
    async handleFilter() {
      const data = await getStaticData(this.listQuery)
      this.desc = data.data
    },
    handleChangeTabs(tab, event) {
      if (tab.name === 'chart') {
        this.$nextTick(() => {
          this.myChart = this.$echarts.init(this.$refs.echart)
          this.drawLine()
        })
      }
    },
    async getOrgOption() {
      const data = await getOrgList(this.selectListQuery)
      this.selectListQuery.page++
      this.originationOptions = [...this.originationOptions, ...data.data.list.map((item) => {
        return { id: item.id, label: item.name }
      })]
      if (+data.data.pagesize > data.data.list.length) {
        this.scrollToEnd = true
      }
    },
    async changeOrg(value) {
      this.listQuery.userIds = []
      this.listQuery.statisItem = []
      if (value) {
        const userList = await getOrgsUser({ organization_id: value })
        this.userListOptions = userList.data
      }
    },
    async drawLine() {
      const list = await getChartData(this.listQuery)
      this.myChart.setOption(list.data)
    },
    async loadMore() {
      if (!this.scrollToEnd) {
        await this.getOrgOption()
      }
    },
    async remoteFind(val) {
      this.selectListQuery.page = 1
      this.selectListQuery.name = val
      this.scrollToEnd = false
      const data = await getOrgList(this.selectListQuery)
      this.originationOptions = data.data.list.map((item) => {
        return { id: item.id, label: item.name }
      })
    },
    async clearSelect() {
      this.selectListQuery.page = 1
      this.selectListQuery.name = null
      this.originationOptions = [{ id: -1, label: '无所属医院用户' }]
      await this.getOrgOption()
    }
  }
}
</script>
<style lang="">

</style>
