import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/group/list',
    method: 'get',
    params
  })
}

export function getGroupAttendList(groupId) {
  return request({
    url: '/group/attendees',
    method: 'get',
    params: { groupId }
  })
}

export function saveGroupTag(params) {
  return request({
    url: '/group/tag',
    method: 'put',
    data: params
  })
}
