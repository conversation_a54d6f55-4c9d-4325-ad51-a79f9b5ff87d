<template>
  <div>
    {{ tagProps | keyNullFilter }}
    <el-tag
      v-for="(item, index) in tagProps"
      :key="index"
      effect="plain"
      @click="onClipboard(item, $event)"
    >
      {{ item }}
    </el-tag>
  </div>
</template>
<script>
import clip from '@/utils/clipboard'

export default {
  name: 'KeyTag',
  filters: {
    keyNullFilter(val) {
      if (val == null || val === undefined || val === '') {
        return '-'
      } else {
        // 使用el-tag格式化
        // return val
        return null
      }
    }
  },
  props: {
    tagProps: {
      type: Array,
      default: () => [],
      require: true
    }
  },
  data() {
    return {
    }
  },
  methods: {
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    }
  }
}
</script>
<style scoped>
.el-tag {
  display: block;
  overflow: hidden;
  width: fit-content;
  min-width: 50px;
  height: fit-content;
  line-height: 20px;
  margin: 0 auto;
  margin-bottom: 10px;
  font-size: 13px;
  word-wrap: break-word;
  white-space: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -o-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.el-tag:hover {
  background-color: #d6d6d610;
}
</style>
