<template>
  <div class="dashboard-container">
    <div class="dashboard-text">name: {{ name||nickname }}</div>
    <div class="dashboard-text">当前时间: {{ new Date() }}</div>
    <div v-if="isDevelopment" class="dashboard-text">当前在开发环境下</div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  computed: {
    ...mapGetters([
      'name',
      'nickname'
    ]),
    isDevelopment() {
      if (process.env.NODE_ENV === 'development') {
        return true
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
