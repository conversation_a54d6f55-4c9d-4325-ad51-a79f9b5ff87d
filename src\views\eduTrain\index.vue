<template>
  <div class="app-container">
    <h1>{{ lang.title }}</h1>
    <div class="filter-container">
      <el-button type="primary" icon="el-icon-plus" class="add-btn" @click="handleAddProject">{{ lang.buttons.addProject }}</el-button>
      <!-- 语言切换 -->
      <div class="language-switch">
        <div class="lang-toggle" :class="{'active': currentLanguage === 'en'}" @click="toggleLanguage">
          <span class="lang-text">{{ currentLanguage === 'zh' ? '汉' : 'EN' }}</span>
        </div>
      </div>
      <!-- 搜索 -->
      <el-form :inline="true" :model="listQuery">
        <el-form-item :label="lang.filter.nameLabel">
          <el-input v-model="listQuery.name" :placeholder="lang.filter.namePlaceholder" clearable />
        </el-form-item>
        <!-- <el-form-item :label="lang.filter.idLabel">
          <el-input v-model="listQuery.id" :placeholder="lang.filter.idPlaceholder" clearable oninput="this.value=this.value.replace(/[^\d]/g,'')" />
        </el-form-item> -->
        <el-form-item :label="lang.filter.specialtyLabel">
          <el-select v-model="listQuery.specialty" :placeholder="lang.filter.specialtyPlaceholder" clearable>
            <el-option v-for="item in specialtyOptions" :key="item.value" :label="getSpecialtyLabel(item.value)" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="lang.filter.countryLabel">
          <el-select v-model="listQuery.country" :placeholder="lang.filter.countryPlaceholder" clearable>
            <el-option v-for="item in countryOptions" :key="item.value" :label="getCountryLabel(item.value)" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">{{ lang.buttons.search }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">{{ lang.buttons.reset }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 子表格 -->
    <el-table
      ref="eduTable"
      v-loading="listLoading"
      :data="computedList"
      :element-loading-text="lang.table.loadingText"
      border
      fit
      highlight-current-row
      row-key="id"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <div :key="props.row.id + '_' + props.row.status" style="padding: 0px 12px 0px 50px;">
            <el-table :data="props.row.assessments" style="width: 100%">
              <el-table-column :label="lang.table.header.requirement" prop="name" />
              <el-table-column :label="lang.table.header.assessmentType" prop="type">
                <template slot-scope="scope">
                  <span>{{ scope.row.type === 'upload' ? lang.options.assessmentType.upload : lang.options.assessmentType.quiz }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column :label="lang.table.header.assessmentMode" prop="mode">
                <template slot-scope="scope">
                  <span>{{ scope.row.mode === 'single' ? lang.options.assessmentMode.single : scope.row.mode === 'multiple' ? lang.options.assessmentMode.multiple : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="lang.table.header.deadline" prop="deadline">
                <template slot-scope="scope">
                  <span>{{ scope.row.deadline || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="lang.table.header.attemptsAllowed" prop="attemptsAllowed">
                <template slot-scope="scope">
                  <span>{{ scope.row.attemptsAllowed || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="lang.table.header.minPassQuestions" prop="minPassQuestions">
                <template slot-scope="scope">
                  <span>{{ scope.row.minPassQuestions || '-' }}</span>
                </template>
              </el-table-column> -->
              <el-table-column :label="lang.table.header.actions" width="180" align="center">
                <template slot-scope="scope">
                  <div class="action-buttons-expand">
                    <el-button v-if="scope.row.type === 'quiz'" size="mini" align="center" type="info" @click="handleConfig(props.row, scope.row)">{{ lang.buttons.config }}</el-button>
                    <span v-else>-</span>
                    <el-button v-if="scope.row.type === 'quiz' && (!scope.row.count || scope.row.count === 0)" size="mini" align="center" type="success" @click="handleOpenUploadDialog(scope.row.testID)">{{ lang.buttons.upload }}</el-button>
                    <el-button v-else-if="scope.row.type === 'quiz' && scope.row.count > 0" size="mini" align="center" type="primary" @click="handleExamEdit(scope.row.testID)">{{ lang.buttons.edit }}</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <!-- 主表格 -->
      <el-table-column :label="lang.table.header.name" align="center">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.specialty" align="center">
        <template slot-scope="scope">
          {{ getSpecialtyLabel(scope.row.specialty) }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.country" align="center">
        <template slot-scope="scope">
          {{ getCountryLabel(scope.row.country) }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.kol" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.adminList && scope.row.adminList.length > 0">
            {{ scope.row.adminList.map(admin => admin.nickname).join(', ') }}
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.reqCount" align="center">
        <template slot-scope="scope">
          {{ scope.row.reqCount }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.createTime" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.expireTime" align="center">
        <template slot-scope="scope">
          {{ scope.row.expireTime }}
        </template>
      </el-table-column>
      <el-table-column :label="lang.table.header.status" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === statusMap.active ? 'success' : scope.row.status === statusMap.pending ? 'info' : 'warning'">
            {{ scope.row.status === statusMap.active ? lang.table.status.active : scope.row.status === statusMap.pending ? lang.table.status.pending : lang.table.status.inactive }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 按钮 -->
      <el-table-column :label="lang.table.header.actions" align="center" width="200">
        <template slot-scope="scope">
          <el-button class="action-button" size="mini" type="primary" @click="handleEdit(scope.row)">
            {{ lang.buttons.edit }}
          </el-button>
          <el-popconfirm
            v-if="scope.row.status === statusMap.active"
            class="action-popconfirm"
            :title="lang.messages.deactivateConfirmTitle.replace('{name}', scope.row.name)"
            icon="el-icon-warning"
            icon-color="red"
            :confirm-button-text="lang.buttons.confirm"
            :cancel-button-text="lang.buttons.cancel"
            confirm-button-type="danger"
            @confirm="handleActivate(scope.row)"
          >
            <el-button
              slot="reference"
              class="action-button"
              size="mini"
              type="danger"
            >
              {{ lang.buttons.deactivate }}
            </el-button>
          </el-popconfirm>
          <el-button
            v-else
            class="action-button"
            size="mini"
            type="success"
            @click="handleActivate(scope.row)"
          >
            {{ lang.buttons.activate }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑项目信息弹窗 -->
    <el-dialog
      ref="projectDialog"
      :title="currentDialogTitle"
      :visible.sync="dialogVisible"
      width="fit-content"
      :close-on-click-modal="false"
      custom-class="project-dialog"
      @close="handleDialogClose"
    >
      <el-form ref="projectForm" :model="projectForm" :rules="projectFormRules" label-width="auto">
        <el-form-item :label="lang.dialog.form.name" prop="name">
          <el-input v-model="projectForm.name" :placeholder="lang.dialog.form.namePlaceholder" />
        </el-form-item>
        <el-form-item :label="lang.dialog.form.location" prop="country">
          <div class="location-row">
            <el-select v-model="projectForm.country" :placeholder="lang.dialog.form.countryPlaceholder" class="country-select">
              <el-option v-for="item in countryOptions" :key="item.value" :label="getCountryLabel(item.value)" :value="item.value" />
            </el-select>
            <el-input v-model="projectForm.address" :placeholder="lang.dialog.form.addressPlaceholder" class="address-input" />
          </div>
        </el-form-item>
        <el-form-item :label="lang.dialog.form.specialty" prop="specialty">
          <el-select v-model="projectForm.specialty" :placeholder="lang.dialog.form.specialtyPlaceholder">
            <el-option v-for="item in specialtyOptions" :key="item.value" :label="getSpecialtyLabel(item.value)" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="lang.dialog.form.kol" prop="adminList">
          <el-select
            v-model="projectForm.adminList"
            multiple
            filterable
            remote
            :remote-method="fetchAdminList"
            :loading="adminListLoading"
            :placeholder="lang.dialog.form.kolPlaceholder"
            value-key="id"
            class="admin-select"
          >
            <el-option
              v-for="(admin, index) in adminOptions"
              :key="admin.id + '_' + index"
              :label="admin.nickname"
              :value="admin"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="lang.dialog.form.requirement">
          <div class="assessment-container">
            <div v-for="(asst, index) in projectForm.assessments" :key="index" class="assessment-entry">
              <el-form-item
                :prop="'assessments.' + index + '.name'"
                :rules="[{ required: true, message: ' ', trigger: 'blur' }]"
                style="flex: 1; margin-right: 10px; margin-bottom: 0;"
              >
                <el-input v-model="asst.name" :placeholder="lang.dialog.form.assessmentNamePlaceholder" class="assessment-name-input" />
              </el-form-item>
              <el-form-item
                :prop="'assessments.' + index + '.type'"
                :rules="[{ required: true, message: ' ', trigger: 'change' }]"
                style="width: 150px; margin-right: 10px; margin-bottom: 0;"
              >
                <el-select v-model="asst.type" :placeholder="lang.dialog.form.assessmentTypePlaceholder" :disabled="isActivatedBefore" class="assessment-type-input">
                  <el-option :label="lang.options.assessmentType.upload" value="upload" />
                  <el-option :label="lang.options.assessmentType.quiz" value="quiz" />
                </el-select>
              </el-form-item>
              <el-button class="remove-requirement-btn" :disabled="isNewProject" type="danger" icon="el-icon-delete" @click="removeAssessment(index)" />
            </div>
            <el-button type="primary" icon="el-icon-plus" :disabled="isNewProject" @click="addAssessmentRow">{{ lang.buttons.addAssessment }}</el-button>
          </div>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ lang.buttons.cancel }}</el-button>
        <el-button type="primary" @click="saveProject">{{ lang.buttons.save }}</el-button>
      </span>
    </el-dialog>
    <!-- 子表格上传弹窗 -->
    <upload-dialog :show.sync="uploadDialogVisible" :active-language="currentLanguage" :test-id="currentTestID" />
    <!-- 子表格考核配置弹窗 -->
    <config-dialog
      :visible.sync="assessmentConfigDialogVisible"
      :assessment-data="currentAssessmentForConfig"
      :active-language="currentLanguage"
      @save="handleSaveAssessmentConfig"
    />
  </div>
</template>

<script>
import { zh, en } from './lang.js'
import { mapGetters } from 'vuex'
import UploadDialog from './uploadDialog.vue'
import ConfigDialog from './configDialog.vue'
import { eduService } from '@/api/education.js'

export default {
  name: 'EduTrain',
  components: {
    Pagination: () => import('@/components/Pagination'),
    UploadDialog,
    ConfigDialog
  },
  data() {
    return {
      currentLanguage: 'zh',
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        id: '',
        specialty: '',
        country: '',
        name: ''
      },
      specialtyOptions: [
        { value: 'cardiac' },
        { value: 'abdomen' },
        { value: 'obgyn' },
        { value: 'superficial' }
      ],
      countryOptions: [],
      statusMap: {
        pending: '1',
        active: '2',
        inactive: '3'
      },
      typeMap: {
        upload: 1,
        quiz: 2
      },
      modeMap: {
        single: 1,
        multiple: 2
      },
      dialogVisible: false,
      projectForm: {
        name: '',
        country: '',
        address: '',
        specialty: '',
        adminList: [],
        assessments: [{ title: '', type: '' }]
      },
      currentDialogTitle: '',
      uploadDialogVisible: false,
      currentTestID: null,
      assessmentConfigDialogVisible: false,
      currentProjectForConfig: null,
      currentAssessmentForConfig: null,
      projectFormRules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }],
        country: [{ required: true, message: ' ', trigger: 'change' }],
        specialty: [{ required: true, message: ' ', trigger: 'change' }],
        adminList: [{ required: true, type: 'array', message: ' ', trigger: 'change' }]
      },
      adminOptions: [],
      adminListLoading: false
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ]),
    lang() {
      return this.currentLanguage === 'zh' ? zh : en
    },
    computedList() {
      // 返回格式化后的列表，createTime和expireTime转为YYYY-MM-DD HH:mm:ss
      const formatDate = (val) => {
        if (!val) return '-'
        // 检查是否为秒级时间戳（10位数）并转换为毫秒
        const timestamp = String(val).length === 10 ? Number(val) * 1000 : Number(val)
        const d = new Date(timestamp)
        if (isNaN(d.getTime())) return '-'

        const year = d.getFullYear()
        const month = (d.getMonth() + 1).toString().padStart(2, '0')
        const day = d.getDate().toString().padStart(2, '0')
        const hours = d.getHours().toString().padStart(2, '0')
        const minutes = d.getMinutes().toString().padStart(2, '0')
        const seconds = d.getSeconds().toString().padStart(2, '0')

        if (this.currentLanguage === 'zh') {
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        } else {
          return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`
        }
      }
      return this.list.map(item => ({
        ...item,
        createTime: formatDate(item.createTime),
        expireTime: item.expireTime ? formatDate(item.expireTime) : '-'
      }))
    },
    isNewProject() { // 检测是否为新创建的项目
      return !!(this.dialogVisible && this.projectForm.id)
    },
    isActivatedBefore() { // 检测是否为激活过的项目
      return this.projectForm.status === this.statusMap.active || this.projectForm.status === this.statusMap.inactive
    }
  },
  created() {
    this.currentLanguage = this.language
    this.getList()
    this.fetchCountryList()
  },
  methods: {
    toggleLanguage() {
      this.currentLanguage = this.currentLanguage === 'zh' ? 'en' : 'zh'
      this.$store.dispatch('app/setLanguage', this.currentLanguage)
    },
    handleDialogClose() {
      if (this.$refs.projectForm) {
        this.$refs.projectForm.clearValidate()
      }
    },
    fetchAdminList(query) {
      if (!query) {
        if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
          const adminMap = new Map()
          this.projectForm.adminList.forEach(admin => {
            if (typeof admin === 'object' && admin.id) {
              adminMap.set(admin.id, admin)
            }
          })
          this.adminOptions = Array.from(adminMap.values())
        } else {
          this.adminOptions = []
        }
        return
      }
      this.adminListLoading = true
      eduService.getAdminList({ login_name: query }).then(response => {
        this.adminListLoading = false
        if (response.code === 200 && response.data) {
          const selectedIds = new Set()
          if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
            this.projectForm.adminList.forEach(admin => {
              const id = typeof admin === 'object' ? admin.id : admin
              selectedIds.add(id)
            })
          }
          // 防止重复
          const newOptions = []
          // 先添加已选择的管理员（KOL）
          if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
            this.projectForm.adminList.forEach(admin => {
              if (typeof admin === 'object' && admin.id) {
                newOptions.push(admin)
              }
            })
          }
          // 添加搜索结果中未选择的管理员（KOL）
          if (response.data.list && response.data.list.length > 0) {
            response.data.list.forEach(admin => {
              if (!selectedIds.has(admin.id)) {
                newOptions.push(admin)
              }
            })
          }
          this.adminOptions = newOptions
        } else {
          // 如果请求失败，保留已选择的管理员（KOL）
          if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
            const adminMap = new Map()
            this.projectForm.adminList.forEach(admin => {
              if (typeof admin === 'object' && admin.id) {
                adminMap.set(admin.id, admin)
              }
            })
            this.adminOptions = Array.from(adminMap.values())
          } else {
            this.adminOptions = []
          }
        }
      }).catch(() => {
        this.adminListLoading = false
        // 出错时保留已选择的管理员（KOL）
        if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
          const adminMap = new Map()
          this.projectForm.adminList.forEach(admin => {
            if (typeof admin === 'object' && admin.id) {
              adminMap.set(admin.id, admin)
            }
          })
          this.adminOptions = Array.from(adminMap.values())
        } else {
          this.adminOptions = []
        }
      })
    },
    getList() {
      this.listLoading = true
      // 准备请求参数
      const data = {
        page: this.listQuery.page,
        pagesize: this.listQuery.limit
      }

      // 只有在有值时才添加搜索参数
      if (this.listQuery.country) {
        data.countryCode = this.listQuery.country
      }
      if (this.listQuery.name && this.listQuery.name.trim()) {
        data.name = this.listQuery.name.trim()
      }
      if (this.listQuery.id && this.listQuery.id.trim()) {
        data.id = this.listQuery.id.trim()
      }
      if (this.listQuery.specialty) {
        data.specialty = this.listQuery.specialty
      }

      // 调用API获取数据
      eduService.getProjectList(data)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = response.data.list.map(item => {
              // 返回格式化的项目数据，保持adminList原样
              return {
                ...item,
                id: item._id,
                reqCount: Array.isArray(item.testList) ? item.testList.length : 0,
                adminList: item.adminList || [],
                assessments: Array.isArray(item.testList) ? item.testList.map(test => {
                  // 获取类型和模式的反向映射
                  const typeKey = Object.keys(this.typeMap).find(key => this.typeMap[key] === test.type) || 'upload'
                  // 考核项格式化
                  return {
                    name: test.title,
                    testID: test.testID,
                    type: typeKey,
                    count: test.questionCount
                  }
                })
                  : [],
                status: item.status ? item.status.toString() : this.statusMap.pending,
                createTime: item.createdAt,
                expireTime: item.endTime,
                country: item.countryCode,
                address: item.address
              }
            })
            this.total = response.data.count || 0 // 分页总数
          } else {
            this.$message.error(this.lang.messages.loadError)
            this.list = []
            this.total = 0
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('获取教培项目列表失败:', error)
          this.$message.error(this.lang.messages.loadError)
          this.list = []
          this.total = 0
          this.listLoading = false
        })
    },
    handleSearch() {
      this.listQuery.page = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        id: '',
        specialty: '',
        country: '',
        name: ''
      }
      this.getList()
    },
    getSpecialtyLabel(value) {
      return this.lang.options.specialty[value] || value
    },
    getCountryLabel(value) {
      const country = this.countryOptions.find(item => item.value === value)
      return country ? (this.currentLanguage === 'zh' ? country.zh : country.en) : value
    },
    handleEdit(row) {
      // 处理编辑操作
      // 深拷贝行数据到 projectForm，确保 assessments 也是新的数组实例
      this.projectForm = JSON.parse(JSON.stringify(row))
      // 如果 row 中可能没有 assessments 属性，或者 assessments 可能为 null/undefined，确保 projectForm.assessments 是一个数组
      if (!this.projectForm.assessments) {
        this.projectForm.assessments = []
      }
      if (this.projectForm.adminList && this.projectForm.adminList.length > 0) {
        // 创建一个映射，确保每个ID只有一个对应的对象
        const adminMap = new Map()
        this.projectForm.adminList.forEach(admin => {
          if (typeof admin !== 'object') {
            return
          }
          // 只添加未添加过的ID
          if (!adminMap.has(admin.id)) {
            adminMap.set(admin.id, { ...admin })
          }
        })
        this.adminOptions = Array.from(adminMap.values())
      } else {
        this.adminOptions = []
      }
      this.currentDialogTitle = this.lang.dialog.title.edit
      this.dialogVisible = true
    },
    handleActivate(row) {
      const newStatus = row.status === this.statusMap.active ? this.statusMap.inactive : this.statusMap.active

      // 如果是要激活项目，需要校验quiz类型的考核项是否有题目
      if (newStatus === this.statusMap.active) {
        // 检查是否有quiz类型且questionCount为0的考核项
        const emptyQuizAssessments = row.assessments?.filter(assessment =>
          assessment.type === 'quiz' && (assessment.count === 0 || !assessment.count)
        ) || []

        if (emptyQuizAssessments.length > 0) {
          const assessmentNames = emptyQuizAssessments.map(assessment => assessment.name).join('、')
          this.$message.error(this.lang.messages.quizWithoutQuestionsError?.replace('{names}', assessmentNames))
          return
        }
      }

      this.listLoading = true

      eduService.updateStatus({
        id: row.id,
        status: newStatus
      })
        .then(response => {
          if (response.code === 200) {
            if (newStatus === this.statusMap.active) {
              this.$message.success(this.lang.messages.activatedSuccess.replace('{name}', row.name))
            } else {
              this.$message.warning(this.lang.messages.deactivatedSuccess.replace('{name}', row.name))
            }
            this.getList()
          } else {
            this.$message.error(response.message)
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('更新状态失败:', error)
          this.$message.error(this.lang.messages.updateFailed)
          this.listLoading = false
        })
    },
    // 新增项目相关方法
    handleAddProject() {
      this.resetProjectForm() // 先清空表单
      this.currentDialogTitle = this.lang.dialog.title.add
      this.dialogVisible = true
    },
    resetProjectForm() {
      this.projectForm = {
        name: '',
        country: '',
        address: '',
        specialty: '',
        adminList: [],
        assessments: [{ name: '', type: '' }]
      }
    },
    addAssessmentRow() {
      this.projectForm.assessments.push({ title: '', type: '' })
    },
    removeAssessment(index) {
      this.projectForm.assessments.splice(index, 1)
    },
    saveProject() {
      this.$refs.projectForm.validate(formValid => {
        let customChecksPass = true

        if (!this.projectForm.name || !this.projectForm.country || !this.projectForm.specialty || !this.projectForm.adminList || this.projectForm.adminList.length === 0) {
          this.$message.error(this.lang.messages.projectFormValidationError)
          customChecksPass = false
        }

        // 在新增模式下，确保assessments中的每一项在保存前都有默认的配置字段
        if (!this.isNewProject && this.projectForm.assessments && this.projectForm.assessments.length > 0) {
          this.projectForm.assessments.forEach(assessment => {
            if (assessment.deadline === undefined) assessment.deadline = '' // Or a sensible default
            if (assessment.attemptsAllowed === undefined) assessment.attemptsAllowed = assessment.type === 'upload' ? 1 : 0
            if (assessment.minPassQuestions === undefined) assessment.minPassQuestions = 0
          })
        }

        if (customChecksPass && this.projectForm.assessments && this.projectForm.assessments.length === 0 && !this.isNewProject) {
          this.$message.error(this.lang.messages.assessmentRequired)
          customChecksPass = false
        }

        if (customChecksPass && this.projectForm.assessments && this.projectForm.assessments.length > 0) {
          for (let i = 0; i < this.projectForm.assessments.length; i++) {
            const assessment = this.projectForm.assessments[i]
            if (!assessment.name || assessment.name.trim() === '') {
              this.$message.error(this.lang.messages.assessmentNameRequired.replace('{index}', i + 1))
              customChecksPass = false
              break
            }
            if (!assessment.type) {
              this.$message.error(this.lang.messages.assessmentTypeRequired.replace('{index}', i + 1))
              customChecksPass = false
              break
            }
          }
        }

        if (formValid && customChecksPass) {
          if (this.isNewProject) {
            this.updateExistingProject()
          } else {
            this.createNewProject()
          }
        } else {
          return false
        }
      })
    },
    createNewProject() {
      console.log('保存新项目', this.projectForm)
      // 准备提交到后端的数据
      const selectedCountry = this.countryOptions.find(c => c.value === this.projectForm.country) || {}
      const requestData = {
        name: this.projectForm.name,
        countryCode: this.projectForm.country,
        countryName: selectedCountry.zh || selectedCountry.en || '',
        specialty: this.projectForm.specialty,
        address: this.projectForm.address || '',
        adminUidList: this.projectForm.adminList.map(item => typeof item === 'object' ? item.id : item),
        testList: (this.projectForm.assessments || []).map(assessment => ({
          ...assessment,
          title: assessment.name, // 使用name字段转换为后端需要的title字段
          type: this.typeMap[assessment.type] || 1
        }))
      }

      // 调用创建项目API
      this.listLoading = true
      eduService.createProject(requestData)
        .then(response => {
          if (response.code === 200) {
            this.$message.success(this.lang.messages.addSuccess)
            this.dialogVisible = false
            this.getList() // 刷新列表
          } else {
            this.$message.error(response.message || this.lang.messages.addFailed)
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('创建项目失败:', error)
          this.$message.error(this.lang.messages.addFailed)
          this.listLoading = false
        })
    },
    updateExistingProject() {
      console.log('更新项目', this.projectForm)
      // 准备更新项目的数据
      const selectedCountry = this.countryOptions.find(c => c.value === this.projectForm.country) || {}
      const requestData = {
        id: this.projectForm.id, // 项目ID，从列表中获取
        name: this.projectForm.name,
        countryCode: this.projectForm.country,
        countryName: selectedCountry.zh || selectedCountry.en || '',
        specialty: this.projectForm.specialty,
        address: this.projectForm.address || '',
        adminUidList: this.projectForm.adminList.map(item => typeof item === 'object' ? item.id : item),
        testList: (this.projectForm.assessments || []).map(assessment => ({
          ...assessment,
          title: assessment.name, // 使用name字段转换为后端需要的title字段
          type: this.typeMap[assessment.type] || 1
        }))
      }

      // 调用更新项目API
      this.listLoading = true
      eduService.updateProject(requestData)
        .then(response => {
          if (response.code === 200) {
            this.$message.success(this.lang.messages.updateSuccess)
            this.dialogVisible = false
            this.getList() // 刷新列表
          } else {
            this.$message.error(response.message || this.lang.messages.updateFailed)
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('更新项目失败:', error)
          this.$message.error(this.lang.messages.updateFailed)
          this.listLoading = false
        })
    },
    handleConfig(parentRow, subRow) {
      this.currentProjectForConfig = parentRow
      this.listLoading = true // 开始加载状态

      eduService.getAssessDetail({ testID: subRow.testID })
        .then(response => {
          if (response.code === 200 && response.data) {
            const updatedSubRow = {
              ...subRow,
              paperInfo: response.data.paperInfo,
              // mode: response.data.retryType ? Object.keys(this.modeMap).find(key => this.modeMap[key] === response.data.retryType) : (response.data.maxRetry > 1 ? 'multiple' : 'single'),
              mode: response.data.retryType ? Object.keys(this.modeMap).find(key => this.modeMap[key] === response.data.retryType) : '',
              deadline: response.data.deadline ? new Date(response.data.deadline * 1000).toISOString().split('T')[0] : '',
              attemptsAllowed: response.data.maxRetry !== undefined ? response.data.maxRetry : (subRow.type === 'upload' ? 1 : 0),
              minPassQuestions: response.data.minPassQuestions !== undefined ? response.data.minPassQuestions : 0,
              testID: subRow.testID,
              type: subRow.type,
              questionCount: response.data.questionCount,
              antiCheatEnabled: response.data.antiCheatEnabled
            }

            // 将父行状态附加到传递给配置对话框的数据中
            const assessmentConfigData = {
              ...JSON.parse(JSON.stringify(updatedSubRow)),
              project: {
                status: parentRow.status,
                type: parentRow.type,
                id: parentRow.id
              }
            }
            this.currentAssessmentForConfig = assessmentConfigData
            this.assessmentConfigDialogVisible = true
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('获取考核详情失败:', error)
          this.$message.error(this.lang.messages.loadConfigError || '加载考核配置失败')
          this.listLoading = false
        })
    },
    handleSaveAssessmentConfig() {
      // API 调用和数据更新已在 configDialog.vue 中完成，在这里只需要刷新列表并处理UI状态即可
      this.$message.success(this.lang.messages.configSuccess)
      this.getList() // 刷新列表以获取最新数据

      this.assessmentConfigDialogVisible = false
      // 重置相关状态，确保下次打开对话框时是干净的状态
      this.currentProjectForConfig = null
      this.currentAssessmentForConfig = null
    },
    handleOpenUploadDialog(testID) {
      this.uploadDialogVisible = true
      this.currentTestID = testID
    },
    fetchCountryList() {
      eduService.getCountryList().then(response => {
        if (response.code === 200 && response.data) {
          this.countryOptions = response.data.map(item => ({
            value: item.code,
            zh: item.zh,
            en: item.en
          }))
        } else {
          console.error('获取国家列表失败:', response)
          // 使用语言文件中的国家定义作为备选
          this.useDefaultCountryOptions()
        }
      }).catch(error => {
        console.error('获取国家列表出错:', error)
        // 使用语言文件中的国家定义作为备选
        this.useDefaultCountryOptions()
      })
    },
    useDefaultCountryOptions() {
      const countries = this.lang.options.country
      this.countryOptions = Object.keys(countries).map(key => ({
        value: key,
        zh: this.lang.options.country[key],
        en: this.lang.options.country[key]
      }))
    },
    handleExamEdit(testID) {
      this.listLoading = true
      eduService.getAssessDetail({ testID })
        .then(response => {
          if (response.code === 200 && response.data) {
            const paperInfo = Array.isArray(response.data.paperInfo) && response.data.paperInfo.length > 0
              ? response.data.paperInfo[0]
              : response.data.paperInfo

            this.$store.commit('homework/setUploadPaper', {
              paperInfo: paperInfo
            })
            // 跳转到考试编辑页面
            this.$router.push({
              path: '/eduTrain/exam/1',
              query: { lang: this.currentLanguage, testID: testID }
            })
          } else {
            this.$message.error(this.lang.messages.loadError)
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('获取考核详情失败:', error)
          this.$message.error(this.lang.messages.loadError)
          this.listLoading = false
        })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.add-btn {
  margin-bottom: 10px;
  margin-right: 10px;
}

.language-switch {
  display: inline-block;
  margin-bottom: 10px;
  position: absolute;
  right: 20px;
}

.lang-toggle {
  width: 40px;
  height: 40px;
  background-color: #409EFF;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lang-toggle.active {
  background-color: #5ad2b8;
}

.lang-text {
  color: #fff;
  font-weight: bold;
  font-size: 14px;
  user-select: none;
}

.project-dialog {
  border-radius: 8px;
}

.location-row {
  display: flex;
  align-items: center;
}

.country-select {
  width: 150px;
  margin-right: 10px;
}

.address-input {
  flex: 1;
}

.assessment-container {
  width: 100%;
}

.input-row {
  display: flex;
  margin-bottom: 10px;
}

.assessment-entry {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.assessment-name-input {
  flex: 1;
  margin-right: 10px;
}

.assessment-type-input {
  width: 150px;
  margin-right: 10px;
}

.action-popconfirm {
  margin-left: 10px;
}

.action-buttons-expand {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}
.admin-select {
  width: 100%;
}
</style>
