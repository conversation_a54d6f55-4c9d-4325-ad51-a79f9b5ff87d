import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  })
}

export function updateUser(user) {
  return request({
    url: `/user/${user.id}`,
    method: 'put',
    data: user
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function getList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

export function setMultiUserType(userIds, type) {
  return request({
    url: '/user/type',
    method: 'put',
    data: { userIds, type }
  })
}
