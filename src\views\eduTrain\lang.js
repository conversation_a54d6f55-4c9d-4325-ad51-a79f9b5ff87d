export const zh = {
  title: '教培项目管理',
  filter: {
    idLabel: 'ID',
    idPlaceholder: '项目ID',
    specialtyLabel: '超声亚专业',
    specialtyPlaceholder: '选择超声亚专业',
    countryLabel: '国家/地区',
    countryPlaceholder: '选择国家/地区',
    nameLabel: '项目名称',
    namePlaceholder: '项目名称'
  },
  buttons: {
    search: '搜索',
    reset: '重置',
    edit: '编辑',
    activate: '生效',
    deactivate: '失效',
    addProject: '新增项目',
    add: '添加',
    addAssessment: '添加考核',
    config: '配置',
    confirm: '确定',
    cancel: '取消',
    upload: '上传',
    save: '保存'
  },
  messages: {
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    addSuccess: '添加成功',
    addFailed: '添加失败',
    formValidationError: '表单校验失败',
    projectFormValidationError: '项目内容不能为空',
    assessmentRequired: '至少需要一个考核要求',
    activatedSuccess: '项目 "{name}" 已设为生效',
    deactivatedSuccess: '项目 "{name}" 已设为失效',
    assessmentNameRequired: '第 {index} 个考核要求的名称不能为空',
    assessmentTypeRequired: '第 {index} 个考核要求的类型不能为空',
    deactivateConfirmTitle: '确定要失效项目 "{name}" 吗？',
    configSuccess: '考核配置更新成功',
    loadError: '加载失败',
    configFailed: '配置失败',
    quizWithoutQuestionsError: '考核项 "{names}" 为考试类型但没有题目，无法激活项目'
  },
  table: {
    loadingText: '加载中...',
    header: {
      id: 'ID',
      name: '项目名称',
      specialty: '超声亚专业',
      country: '国家/地区',
      kol: '认证者(KOL)',
      reqCount: '考核数量要求',
      createTime: '创建时间',
      expireTime: '失效时间',
      status: '状态',
      actions: '操作',
      requirement: '考核要求',
      assessmentType: '考核类型',
      assessmentMode: '考核模式',
      deadline: '考核截止时间',
      attemptsAllowed: '作答次数',
      minPassQuestions: '最低通过题数'
    },
    status: {
      active: '生效中',
      inactive: '已失效',
      pending: '待生效'
    }
  },
  dialog: {
    title: {
      add: '新增项目',
      edit: '编辑项目'
    },
    config: {
      titlePrefix: '配置考核',
      assessmentMode: '考核模式',
      assessmentModePlaceholder: '选择考核模式',
      assessmentModeRequired: '请选择考核模式',
      deadline: '考核截止时间',
      deadlinePlaceholder: '选择截止时间',
      deadlineRequired: '请选择考核截止时间',
      attemptsAllowed: '作答次数',
      attemptsAllowedPlaceholder: '请输入作答次数',
      attemptsAllowedRequired: '请输入有效的作答次数',
      attemptsAllowedInvalid: '作答次数必须是大于等于0的整数',
      minPassQuestions: '最低通过题数',
      minPassQuestionsPlaceholder: '请输入最低通过题数',
      minPassQuestionsRequired: '请输入有效的最低通过题数',
      minPassQuestionsInvalid: '最低通过题数必须是大于等于0的整数',
      minPassQuestionsExceedsTotal: '最低通过题数不能超过总题数',
      antiCheatMode: '防作弊模式',
      antiCheatModeRequired: '请选择防作弊模式'
    },
    form: {
      name: '项目名称',
      namePlaceholder: '请输入项目名称',
      location: '所属地址',
      countryPlaceholder: '国家/地区',
      addressPlaceholder: '详细地址',
      specialty: '所属亚专业',
      specialtyPlaceholder: '选择亚专业',
      kol: '认证者(KOL)',
      kolPlaceholder: '请输入认证者姓名，可添加多个',
      requirement: '考核要求',
      assessmentNamePlaceholder: '请输入要求名称',
      assessmentTypePlaceholder: '选择类型'
    }
  },
  options: {
    specialty: {
      cardiac: '心脏超声',
      abdomen: '腹部超声',
      obgyn: '产科超声',
      superficial: '浅表超声'
    },
    country: {
      CN: '中国',
      US: '美国',
      UK: '英国',
      JP: '日本',
      MY: '马来西亚',
      SG: '新加坡'
    },
    assessmentType: {
      upload: '文件提交',
      quiz: '网上作答'
    },
    assessmentMode: {
      single: '单次',
      multiple: '多次'
    },
    common: {
      on: '开',
      off: '关'
    }
  },
  quiz: {
    upload: {
      error: {
        noOptions: '{type}必须包含至少一个选项',
        invalidScore: '{type}的分值必须大于0',
        fieldRequired: '{field}不能为空',
        maxColumns: '{sheetName} 列数超过限制',
        maxRows: '{sheetName} 行数超过限制',
        general: '上传失败',
        fileFormat: '请上传Excel格式的文件(.xlsx或.xls)',
        fileSize: '文件大小不能超过2MB'
      },
      requirements: {
        item5: '文件所含列数请勿超过20列',
        item4: '每次上传的数据行数请勿超过10000',
        item3: '文件大小请勿超过2MB',
        item2: '数据请勿放在合并的单元格中（注：多选题的多个选项可在同一单元格中）',
        item1: '后缀名为xls或者xlsx'
      },
      download_excel_template: '下载Excel模板',
      upload_excel: '上传Excel文件'
    }
  },
  exam: {
    title: '未命名考试',
    questionType: {
      operation: '实操题',
      shortAnswer: '简答题'
    },
    actions: {
      save: '保存',
      cancel: '取消',
      back: '返回'
    },
    questionCount: '共{count}题',
    questionPlaceholder: '请输入题目',
    descriptionPlaceholder: '请输入题目描述',
    deleteConfirm: '确定删除此题目?',
    messages: {
      updateSuccess: '试题更新成功',
      updateFailed: '试题更新失败',
      deleteSuccess: '删除成功',
      invalidImageFormat: '不支持的图片格式',
      imageTooLarge: '图片大小不能超过20MB',
      notInEditMode: '当前不在编辑模式',
      missingTestID: '缺少测试ID，无法保存',
      uploadSuccess: '张图片已上传',
      deleteImageConfirm: '确定要删除这张图片吗？',
      uploadFailed: '上传失败',
      confirmTitle: '提示',
      ossCredentialsFailed: '获取OSS凭证失败'
    }
  }
}

export const en = {
  title: 'Education & Training Program Management',
  filter: {
    idLabel: 'ID',
    idPlaceholder: 'Project ID',
    specialtyLabel: 'Ultrasound Specialty',
    specialtyPlaceholder: 'Select Ultrasound Specialty',
    countryLabel: 'Country/Region',
    countryPlaceholder: 'Country/Region',
    nameLabel: 'Project Name',
    namePlaceholder: 'Project Name'
  },
  buttons: {
    search: 'Search',
    reset: 'Reset',
    edit: 'Edit',
    activate: 'Activate',
    deactivate: 'Deactivate',
    addProject: 'Add Project',
    add: 'Add',
    addAssessment: 'Add Requirement',
    config: 'Config',
    confirm: 'Confirm',
    cancel: 'Cancel',
    upload: 'Upload',
    save: 'Save'
  },
  messages: {
    updateSuccess: 'Update Success',
    updateFailed: 'Update Failed',
    addSuccess: 'Add Success',
    addFailed: 'Add Failed',
    formValidationError: 'Form Validation Failed',
    projectFormValidationError: 'Project content cannot be empty',
    assessmentRequired: 'At least one assessment is required',
    activatedSuccess: 'Project "{name}" set to active',
    deactivatedSuccess: 'Project "{name}" set to inactive',
    assessmentNameRequired: 'Name for requirement #{index} cannot be empty',
    assessmentTypeRequired: 'Type for requirement #{index} cannot be empty',
    deactivateConfirmTitle: 'Are you sure you want to deactivate project "{name}"?',
    configSuccess: 'Assessment configuration updated successfully',
    loadError: 'Load Failed',
    configFailed: 'Configuration Failed',
    quizWithoutQuestionsError: 'Assessment "{names}" is of exam type but has no questions, unable to activate project'
  },
  table: {
    loadingText: 'Loading...',
    header: {
      id: 'ID',
      name: 'Project Name',
      specialty: 'Ultrasound Specialty',
      country: 'Country/Region',
      kol: 'Key Opinion Leader',
      reqCount: 'Requirement Count',
      createTime: 'Creation Time',
      expireTime: 'Expiration Time',
      status: 'Status',
      actions: 'Actions',
      requirement: 'Assessment Requirement',
      assessmentType: 'Assessment Type',
      assessmentMode: 'Assessment Mode',
      deadline: 'Assessment Deadline',
      attemptsAllowed: 'Attempts Allowed',
      minPassQuestions: 'Minimum Pass Questions'
    },
    status: {
      active: 'Active',
      inactive: 'Inactive',
      pending: 'Pending'
    }
  },
  dialog: {
    title: {
      add: 'Add Project',
      edit: 'Edit Project'
    },
    config: {
      titlePrefix: 'Configure Assessment',
      assessmentMode: 'Assessment Mode',
      assessmentModePlaceholder: 'Select Assessment Mode',
      assessmentModeRequired: 'Please select assessment mode',
      deadline: 'Assessment Deadline',
      deadlinePlaceholder: 'Select Deadline',
      deadlineRequired: 'Please select assessment deadline',
      attemptsAllowed: 'Attempts Allowed',
      attemptsAllowedPlaceholder: 'Enter Attempts Allowed',
      attemptsAllowedRequired: 'Please enter a valid number of attempts',
      attemptsAllowedInvalid: 'Attempts allowed must be greater than or equal to 0',
      minPassQuestions: 'Minimum Pass Questions',
      minPassQuestionsPlaceholder: 'Enter minimum pass questions',
      minPassQuestionsRequired: 'Please enter a valid number for minimum pass questions',
      minPassQuestionsInvalid: 'Minimum pass questions must be an integer greater than or equal to 0',
      minPassQuestionsExceedsTotal: 'Minimum pass questions cannot exceed total questions',
      antiCheatMode: 'Anti-cheat Mode',
      antiCheatModeRequired: 'Please select anti-cheat mode'
    },
    form: {
      name: 'Project Name',
      namePlaceholder: 'Enter Project Name',
      location: 'Location',
      countryPlaceholder: 'Country/Region',
      addressPlaceholder: 'Detailed Address',
      specialty: 'Specialty',
      specialtyPlaceholder: 'Select Specialty',
      kol: 'Key Opinion Leaders',
      kolPlaceholder: 'Enter KOL names, multiple entries allowed',
      requirement: 'Requirements',
      assessmentNamePlaceholder: 'Enter Requirement Name',
      assessmentTypePlaceholder: 'Select Type'
    }
  },
  options: {
    specialty: {
      cardiac: 'Cardiac Ultrasound',
      abdomen: 'Abdominal Ultrasound',
      obgyn: 'Obstetric Ultrasound',
      superficial: 'Superficial Ultrasound'
    },
    country: {
      CN: 'China',
      US: 'United States',
      UK: 'United Kingdom',
      JP: 'Japan',
      MY: 'Malaysia',
      SG: 'Singapore'
    },
    assessmentType: {
      upload: 'File Submission',
      quiz: 'Online Quiz'
    },
    assessmentMode: {
      single: 'Single',
      multiple: 'Multiple'
    },
    common: {
      on: 'On',
      off: 'Off'
    }
  },
  quiz: {
    upload: {
      error: {
        noOptions: 'The {type} must contain at least one option',
        invalidScore: 'The score for {type} must be greater than 0',
        fieldRequired: 'The {field} cannot be empty',
        maxColumns: '{sheetName} exceeds maximum columns limit',
        maxRows: '{sheetName} exceeds maximum rows limit',
        general: 'Upload failed',
        fileFormat: 'Please upload an Excel file (.xlsx or .xls)',
        fileSize: 'File size cannot exceed 2MB'
      },
      requirements: {
        item5: 'Please do not exceed 20 columns in the file',
        item4: 'Please do not upload more than 10000 rows of data at a time',
        item3: 'The file size should not exceed 2MB',
        item2: 'Do not place data in merged cells (note: multiple options for multiple-choice questions can be in the same cell)',
        item1: 'The extension name is xls or xlsx'
      },
      download_excel_template: 'Download Template',
      upload_excel: 'Upload Excel File'
    }
  },
  exam: {
    title: 'Unnamed Exam',
    questionType: {
      operation: 'Operation Question',
      shortAnswer: 'Short Answer Question'
    },
    actions: {
      save: 'Save',
      cancel: 'Cancel',
      back: 'Back'
    },
    questionCount: 'Total {count} Questions',
    questionPlaceholder: 'Enter Question',
    descriptionPlaceholder: 'Enter Question Description',
    deleteConfirm: 'Are you sure you want to delete this question?',
    messages: {
      updateSuccess: 'Paper updated successfully',
      updateFailed: 'Failed to update paper',
      deleteSuccess: 'Deleted successfully',
      invalidImageFormat: 'Unsupported image format',
      imageTooLarge: 'Image size cannot exceed 20MB',
      notInEditMode: 'Current not in edit mode',
      missingTestID: 'Missing test ID, unable to save',
      uploadSuccess: 'Image uploaded',
      deleteImageConfirm: 'Are you sure you want to delete this image?',
      uploadFailed: 'Upload failed',
      confirmTitle: 'Prompt',
      ossCredentialsFailed: 'Failed to get OSS credentials'
    }
  }
}

// 可以在这里添加英文或其他语言的翻译
// export const en = { ... };
