import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/channel/list',
    method: 'get',
    params
  })
}

export function getChannelUserList(channelId) {
  return request({
    url: '/channel/user',
    method: 'get',
    params: { channelId }
  })
}

export function getChannelDetail(id) {
  return request({
    url: `/channel/${id}`,
    method: 'get'
  })
}
