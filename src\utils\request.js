import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { getBeforeTimeStampByDay } from '@/utils/index'

// create an axios instance
const service = axios.create({
  timeout: 500000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['authorization'] = getToken()
    }
    const originalParams = config.params || {}
    // 创建一个新的参数对象，避免修改原始对象
    const newParams = { ...originalParams }

    if (originalParams.timeScope && originalParams.timeScope.length > 0) {
      newParams.startTime = getBeforeTimeStampByDay(originalParams.timeScope[0], 0)
      newParams.endTime = getBeforeTimeStampByDay(originalParams.timeScope[1], -1) - 1
    }
    // 删除新参数对象中的 timeScope
    delete newParams.timeScope
    config.params = newParams
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data

    if (res instanceof Blob) {
      return res
    }

    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 200) {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      if (res.code === 403) {
        Message({
          message: '登录已过期',
          type: 'error',
          duration: 5 * 1000
        })
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
