<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.env" placeholder="环境" clearable style="width: 130px" class="filter-item">
        <el-option key="dev" label="dev" value="dev" />
        <el-option key="beta" label="beta" value="beta" />
        <el-option key="prod" label="prod" value="prod" />
        <el-option key="prod-showroom" label="prod-showroom" value="prod-showroom" />
        <el-option key="ce-dev" label="ce-dev" value="ce-dev" />
        <el-option key="ce" label="ce" value="ce" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="channelId">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="onClipboard(row.channel_name, $event)">{{ row.channel_name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="直播间人数" width="110" align="center">
        <template slot-scope="{row}">
          <span>{{ row.user_count }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="updated_at" label="更多" width="200">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button type="primary" size="mini" @click="showDialog(row.userList)">查看详情</el-button>
            <el-button type="danger" size="mini" @click="forceClose(row.channel_name)">强制关闭直播间</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="handleFilter" />
    <el-dialog
      title="展示数据"
      :visible.sync="dialogVisible"
      width="73%"
    >
      <el-button type="primary" size="mini" @click="onClipboard(jsonData, $event)">点击复制</el-button>
      <div class="editor-container">
        <json-viewer
          :value="jsonData"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, closeChannel } from '@/api/agora'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard'
import JsonViewer from 'vue-json-viewer'

const statusOptions = [
  {
    label: '已结束',
    value: 0
  },
  {
    label: '直播中',
    value: 1
  }
]
export default {
  name: 'AgoraChannel',
  components: { Pagination, JsonViewer },

  filters: {
    tagTypeFilter(type) {
      const typeMap = {
        0: 'info',
        1: 'danger'
      }
      return typeMap[type]
    },
    statusFilter(status) {
      const statusMap = {
        0: '已结束',
        1: '直播中'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        env: 'dev'
      },
      statusOptions,
      dialogVisible: false,
      listLoading: true,
      jsonData: {}
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    handleFilter() {
      this.listLoading = true
      getList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.count
        this.listLoading = false
      })
    },
    showDialog(data) {
      this.dialogVisible = true

      this.jsonData = data
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      clip(text, event)
    },
    forceClose(channelId) {
      this.$confirm('请确认此操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        closeChannel({ channelId, env: this.listQuery.env }).then((res) => {
          if (res.code === 200) {
            this.handleFilter()
            this.$message({
              type: 'success',
              message: '关闭成功'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    }
  }
}
</script>

  <style scoped>

  .editor-container{
    position: relative;
    height: 100%;
  }
  .table_more > .el-button {
    margin: 3px
  }
  </style>

