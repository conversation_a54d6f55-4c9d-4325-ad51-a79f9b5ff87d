<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="ID" style="width: 80px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.keyword" type="text" placeholder="关键词模糊查找" style="width: 320px;" class="filter-item" @keyup.enter.native="handleFilter">
        <el-select slot="prepend" v-model="listQuery.keyLanguage" placeholder="选择语言" style="width: 80px;">
          <el-option label="中文" value="cn" />
          <el-option label="英文" value="en" />
          <el-option label="西文" value="es" />
          <el-option label="葡萄牙文" value="ptbr" />
          <el-option label="俄文" value="ru" />
          <el-option label="德语" value="de" />
          <el-option label="意大利语" value="it" />
          <el-option label="法语" value="fr" />
        </el-select>
      </el-input>
      <el-input v-model="listQuery.key" type="text" placeholder="键名精确查找" style="width: 140px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.platform" placeholder="平台" clearable style="width: 80px;" class="filter-item" @keyup.enter.native="handleFilter">
        <el-option label="Web" value="web" />
        <el-option label="PC" value="pc" />
        <el-option label="iOS" value="ios" />
        <el-option label="安卓" value="android" />
      </el-select>
      <el-input v-model="listQuery.uid" placeholder="修改人ID" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.is_review" placeholder="审核状态" clearable style="width: 130px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves :disabled="listLoading" class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button-group class="clearfix update-button">
        <el-button class="text-button" type="text" @click="openInsertDialog()">
          新建
        </el-button>
        <el-button class="text-button" type="text" @click="openExportDialog()">
          导出
        </el-button>
        <el-button type="primary" @click="openUploadDialog()">
          数据导入
          <i class="el-icon-upload el-icon--right">
            <!-- 图标,不需要填充内容 -->
          </i>
        </el-button>
      </el-button-group>
    </div>
    <el-row class="sub-button-row">
      <el-button v-waves round size="small" :disabled="listLoading" @click="handleNullCell('cn')">查看缺失中文的条目</el-button>
      <el-button v-waves round size="small" :disabled="listLoading" @click="handleNullCell('en')">查看缺失英文的条目</el-button>
      <el-button v-waves round size="small" :disabled="listLoading" @click="handleNullCell('es')">查看缺失西文的条目</el-button>
      <el-button v-waves round size="small" :disabled="listLoading" @click="handleNullCell('ptbr')">查看缺失葡萄牙的条目</el-button>
      <el-button v-waves round size="small" :disabled="listLoading" @click="handleNullCell('ru')">查看缺失俄文的条目</el-button>
      <el-dropdown class="more-filter" @command="handleMoreFilterCommand">
        <el-button v-waves round size="small">
          更多高级筛选条件
          <i class="el-icon-arrow-down el-icon--right">
            <!-- 占位 -->
          </i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :disabled="moreListQuery.es_need == 0 || moreListQuery.es_need == 1" command="0">增加条件为无需翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.es_need == 0 || moreListQuery.es_need == 1" command="1">增加条件为需要翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.es_finish == 0 || moreListQuery.es_finish == 1" command="2">增加条件为西文未翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.ptbr_finish == 0 || moreListQuery.ptbr_finish == 1" command="3">增加条件为葡萄牙文未翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.ru_finish == 0 || moreListQuery.ru_finish == 1" command="4">增加条件为俄文未翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.de_finish == 0 || moreListQuery.de_finish == 1" command="5">增加条件为德文未翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.it_finish == 0 || moreListQuery.it_finish == 1" command="6">增加条件为意大利文未翻译的条目</el-dropdown-item>
          <el-dropdown-item :disabled="moreListQuery.fr_finish == 0 || moreListQuery.fr_finish == 1" command="7">增加条件为法文未翻译的条目</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-checkbox-group v-model="isUidShow" size="small">
        <el-checkbox-button size="small" class="uid-switch" label="显示修改人ID" />
      </el-checkbox-group>
    </el-row>
    <el-alert
      class="tips"
      title="在表格表头的单元格边框拖拽可自行调整表格显示大小，点击绿色部分或蓝框部分可快速复制有需要的信息"
      type="info"
      close-text="知道了"
    />
    <div class="more-list-query-tags-group">
      <el-tag
        v-for="tag in moreListQueryTags"
        :key="tag.name"
        class="more-list-query-tags"
        closable
        :type="tag.type"
        @close="handleMoreFilterDelete(tag)"
      >
        {{ tag.name }}
      </el-tag>
    </div>
    <el-table
      v-loading="listLoading"
      element-loading-text="Loading"
      border
      fit
      :span-method="objectSpanMethod"
      :data="list"
      :row-class-name="rowClass"
    >
      <el-table-column align="center" label="中文文本" prop="cn" />
      <el-table-column align="center" label="英文文本" prop="en" />
      <el-table-column class-name="status-col" label="审核状态" width="110" align="center">
        <template slot-scope="{row}">
          {{ row.is_review | statusFilter }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="需要翻译">
          <template slot-scope="{row, $index}">
            <el-switch
              v-model="row.needTranslation"
              @change="(val) => changeNeedTranslate(val, row.id, 'es')"
            />
          </template>
        </el-table-column>
      <el-table-column align="center" label="西文">
        <el-table-column align="center" label="文本" prop="es" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.es | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="葡萄牙文">
        <el-table-column align="center" label="文本" prop="ptbr" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.ptbr | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="俄文">
        <el-table-column align="center" label="文本" prop="ru" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.ru | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="德语">
        <el-table-column align="center" label="文本" prop="de" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.de | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="意大利语">
        <el-table-column align="center" label="文本" prop="it" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.it | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="法语">
        <el-table-column align="center" label="文本" prop="fr" />
        <el-table-column align="center" label="已翻译">
          <template slot-scope="{row}">
            {{ row.translated.fr | booleanFilter }}
          </template>
        </el-table-column>
      </el-table-column>
      <!-- <el-table-column align="center" label="对应ID" width="70" prop="id" /> -->
      <el-table-column align="center" label="键名" prop="key">
        <template slot-scope="{row}">
          <el-tag v-waves class="key_tags" effect="plain" @click="onClipboard(row.key, $event)">{{ row.key }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="使用平台" width="80">
        <template slot-scope="{row}">
          {{ usedPlatform(row) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="上次修改人" width="150">
        <template slot-scope="{row}">
          <el-button v-waves type="success" size="mini" @click="onClipboard(row.nickname, $event)">{{ row.nickname }}</el-button>
          <el-divider v-if="isUidShow" />
          <el-button v-if="isUidShow" v-waves type="success" size="mini" @click="onClipboard(row.uid, $event)">{{ row.uid }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="上次修改时间" width="180">
        <template slot-scope="{row}">
          <span>{{ row.updated_at | formatTime }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="updated_at" label="更多" width="150" fixed="right">
        <template slot-scope="{row}">
          <div class="table_more">
            <el-button-group class="more-button-group">
              <el-button type="primary" size="mini" @click="openEditDialog(row)">编辑</el-button>
              <el-popconfirm
                confirm-button-text="确定"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                title="确定删除吗？"
                @confirm="submitDeleteMultilingual(row.id)"
              >
                <el-button slot="reference" type="danger" size="mini">删除</el-button>
              </el-popconfirm>
            </el-button-group>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pagesize"
      @pagination="findList(listQuery)"
    />

    <!-- 上传对话框 -->
    <el-dialog
      id="uploadDialog"
      v-loading.fullscreen.lock="isUploadLoading"
      element-loading-text="拼命处理数据中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      title="上传文件"
      :visible.sync="uploadDialogVisible"
      modal
      :close-on-click-modal="false"
      :before-close="closeUploadDialog"
    >
      <el-tabs v-model="activeUploadName" type="card" :before-leave="clearFileList">
        <el-tab-pane label="从Excel总体导入（推荐）" name="excel" lazy>
          <el-upload
            ref="uploadMultilingualFromExcel"
            v-model="fileList"
            :limit="1"
            drag
            with-credentials
            show-file-list
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            action="#"
            :file-list="fileList"
            :auto-upload="false"
            :on-error="handleUploadError"
            :on-remove="handleUploadRemove"
            :on-change="handleUploadChange"
          >
            <i class="el-icon-upload">
              <!-- 图标,不需要填充内容 -->
            </i>
            <div class="el-upload__text">将单个文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>只能上传一个标准化的Excel文件(.xlsx), 且不超过10MB </p>
              <p>文件列表最右侧可删除当前已选中文件</p>
            </div>
          </el-upload>
          <el-divider />
          <p class="sample-info">首次上传请
            <el-button
              v-loading.fullscreen.lock="isSampleLoading"
              element-loading-text="下载示例中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
              type="text"
              @click="submitGetSampleExcel"
            >
              下载模板
              <i class="el-icon-download el-icon--right">
                <!-- 图标不需要填充内容 -->
              </i>
            </el-button>
          </p>
          <p class="sample-info">只有符合模板的Excel文件可被正确识别</p>
          <p class="sample-info">※ 已有的中文文本相同的内容会被Excel内内容覆盖</p>
          <el-button
            class="upload-button"
            type="primary"
            :disabled="!isUploadAvailable"
            @click="submitUploadMultilingualFromExcel"
          >
            上传
          </el-button>
          <el-button type="primary" @click="closeUploadDialog">取消</el-button>
        </el-tab-pane>
        <!-- <el-tab-pane label="按平台分类的类型格式导入" name="types" lazy>
          <el-alert
            class="tips"
            type="warning"
          >
            <template slot="title">
              <h2>危险操作 - 易用性与兼容性问题</h2>
              <p>使用此种方式导入的内容需大量人工干预设置，原因是该类导入方式可能导致大量数据冲突、缺失、无法对应等，产生易用性及兼容性问题。问题发生的可能原因为：</p>
              <p>【1】该导入方式<b>无法修改已审核通过</b>的字段；</p>
              <p>【2】对同一语言文本设置了多个不同的字符串，而该字符串又对应了不同的另一语言文本；</p>
              <p>【3】上传了外语语言，且对应了多个key，无法合并；</p>
              <p>【4】其它预想外的错误操作或错误文本等。</p>
            </template>
          </el-alert>
          <el-radio v-model="uploadMultilingualByPlatformsRatio" label="cn">中文</el-radio>
          <el-radio v-model="uploadMultilingualByPlatformsRatio" label="en">英文 (English)</el-radio>
          <el-radio v-model="uploadMultilingualByPlatformsRatio" label="es">西文 (Español)</el-radio>
          <el-radio v-model="uploadMultilingualByPlatformsRatio" label="ptbr">葡萄牙文 (Português)</el-radio>
          <el-radio v-model="uploadMultilingualByPlatformsRatio" label="ru">俄文 (Русский язык)</el-radio>
          <el-upload
            id="multilingualByPlatformsUploader"
            ref="uploadMultilingualByPlatforms"
            v-model="fileList"
            :limit="1"
            drag
            with-credentials
            show-file-list
            :multiple="false"
            :accept="uploadMultilingualByPlatformsAccept"
            action="#"
            :file-list="fileList"
            :auto-upload="false"
            :on-error="handleUploadError"
            :on-remove="handleUploadRemove"
            :on-change="handleUploadChange"
          >
            <i class="el-icon-upload">
            </i>
            <div class="el-upload__text">将单个符合条件的文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>文件列表最右侧可删除当前已选中文件</p>
              <p>接受文件内容符合格式的以下文件，大小共不超过10MB，文件数量不超过 {{ multipleUploadQuantity }}/1</p>
              <span>Web端 (.js)</span>
              <el-divider direction="vertical" />
              <span>PC端 (.ts)</span>
              <el-divider direction="vertical" />
              <span>安卓端 (.xml)</span>
              <el-divider direction="vertical" />
              <span>iOS端 (.strings)</span>
            </div>
          </el-upload>
          <div>
            <el-checkbox v-model="uploadMultilingualByPlatformsConfirm" label="yes">我了解该种导入方式存在风险、易用性与兼容性问题。</el-checkbox>
          </div>
          <el-button
            class="upload-button"
            type="primary"
            :disabled="!isUploadAvailable"
            @click="submitUploadMultilingualByPlatforms"
          >
            上传
          </el-button>
          <el-button type="primary" @click="closeUploadDialog">取消</el-button>
        </el-tab-pane> -->
      </el-tabs>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog
      id="exportDialog"
      title="导出文件"
      :visible.sync="exportDialogVisible"
      modal
      :close-on-click-modal="false"
      :before-close="closeExportDialog"
    >
      <el-tabs v-model="activeExportName" :before-leave="clearExportParameters" tab-position="left">
        <el-tab-pane label="常规导出" name="general" lazy>
          <ExportCheckbox
            :options="exportTypes"
            @update="getCheckedExportTypes(...arguments)"
            @option="getCheckedMoreOptions(...arguments)"
          />
          <p class="export-info">※ 1. 若选择Excel以外的格式导出, 关键文件名将会自动加上后缀用于区分文件, 使用时请手动删除</p>
          <p class="export-info">※ 2. JS仅包含Web端, XML仅包含Android端, Strings仅包含iOS端</p>
          <p class="export-info">※ 3. 若需要PC端的TS文件，请选择TS-Location导出</p>
          <el-button
            v-loading.fullscreen.lock="isExportLoading"
            class="export-button"
            type="primary"
            element-loading-text="导出数据中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :disabled="!isExportAvailable"
            @click="submitExportMultilingual(checkedExportTypes, checkedMoreOptions)"
          >
            导出
          </el-button>
          <el-button class="export-button clearfix" type="primary" size="default" @click="closeExportDialog()">取消</el-button>
        </el-tab-pane>
        <el-tab-pane label="TS-Location 导出" name="ts-location" lazy>
          <el-upload
            ref="tsLocationExportMultilingualUpload"
            with-credentials
            show-file-list
            accept=".ts"
            action="#"
            :limit="1"
            :on-remove="handleUploadRemove"
            :on-error="handleUploadError"
            :on-change="handleUploadChange"
            :file-list="fileList"
            :auto-upload="false"
          >
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传单个对应文本语言TS文件(.ts)，且不超过10mb，建议上传 Location 完整的文件</div>
          </el-upload>
          <el-divider />
          <el-checkbox-group v-model="checkedTsMoreOptions">
            <el-checkbox label="CN">中文</el-checkbox>
            <el-checkbox label="EN">英文</el-checkbox>
            <el-checkbox label="ES">西文</el-checkbox>
            <el-checkbox label="PTBR">葡萄牙文</el-checkbox>
            <el-checkbox label="RU">俄文</el-checkbox>
            <el-checkbox label="DE">德文</el-checkbox>
            <el-checkbox label="IT">意大利文</el-checkbox>
            <el-checkbox label="FR">法文</el-checkbox>
          </el-checkbox-group>
          <p class="export-info">※ 1. 请先上传带有 Location 参数的TS文件 (.ts)</p>
          <p class="export-info">※ 2. 将会从上传文件匹配Location导出，若单条匹配失败则单条将不会导出</p>
          <el-button
            v-loading.fullscreen.lock="isExportLoading"
            class="export-button"
            type="primary"
            element-loading-text="导出数据中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :disabled="!isTsExportAvailable"
            @click="submitTsLocationExportMultilingual(fileList, checkedTsMoreOptions)"
          >
            导出
          </el-button>
          <el-button class="export-button clearfix" type="primary" size="default" @click="closeExportDialog()">取消</el-button>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 编辑对话框（可封装？）-->
    <div class="dialog_container_box">
      <el-dialog
        v-if="editDialogVisible"
        title="编辑多语言内容"
        :visible.sync="editDialogVisible"
        width="73%"
        modal
        :close-on-click-modal="false"
        :before-close="closeEditDialog"
      >
        <el-alert
          class="tips"
          title="键名不支持修改，请选择删除/添加，任何方式、任何内容的编辑提交（包含无修改的重复提交）都将重置审核状态"
          type="warning"
          close-text="知道了"
        />
        <el-form
          :ref="multilingualEditForm"
          :model="multilingualEditForm"
          :rules="formRules"
          label-position="left"
        >
          <el-divider content-position="left">文本设置</el-divider>
          <el-form-item label="中文文本" size="normal" prop="cn">
            <el-input v-model="multilingualEditForm.cn" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="英文文本" size="normal" prop="en">
            <el-tooltip class="item" effect="dark" content="一键翻译成英文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateEnLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','en')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.en" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="西文文本" size="normal" prop="es">
            <el-tooltip class="item" effect="dark" content="一键翻译成西文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateEsLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','spa')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.es" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="葡萄牙文文本" size="normal" prop="ptbr">
            <el-tooltip class="item" effect="dark" content="一键翻译成葡萄牙文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translatePtbrLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','pt')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.ptbr" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="俄文文本" size="normal" prop="ru">
            <el-tooltip class="item" effect="dark" content="一键翻译成俄文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateRuLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','ru')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.ru" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="德文文本" size="normal" prop="de">
            <el-tooltip class="item" effect="dark" content="一键翻译成德文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateDeLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','de')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.de" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="意大利文文本" size="normal" prop="it">
            <el-tooltip class="item" effect="dark" content="一键翻译成意大利文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateItLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','it')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.it" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="法文文本" size="normal" prop="fr">
            <el-tooltip class="item" effect="dark" content="一键翻译成法文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateFrLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','fra')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.fr" type="textarea" rows="3" />
          </el-form-item>
          <el-divider content-position="left">使用平台</el-divider>
          <div class="platform-switcher">
            <label for="platformWeb">Web</label>
            <el-switch
              id="platformWeb"
              v-model="multilingualEditForm.platform.web"
              name="platformWeb"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformPc">PC</label>
            <el-switch
              id="platformPc"
              v-model="multilingualEditForm.platform.pc"
              name="platformPc"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformIOS">iOS</label>
            <el-switch
              id="platformIOS"
              v-model="multilingualEditForm.platform.ios"
              name="platformIOS"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformAndroid">安卓</label>
            <el-switch
              id="platformAndroid"
              v-model="multilingualEditForm.platform.android"
              name="platformAndroid"
            />
          </div>
          <div class="dialog-footer">
            <el-button type="primary" size="default" @click="submitEditMultilingual(multilingualEditForm, false)">确定</el-button>
            <el-button type="primary" size="default" @click="closeEditDialog()">取消</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>

    <!-- 新建对话框（可封装？） -->
    <div class="dialog_container_box">
      <el-dialog
        v-if="insertDialogVisible"
        title="新增多语言内容"
        :visible.sync="insertDialogVisible"
        width="73%"
        modal
        :close-on-click-modal="false"
        :before-close="closeInsertDialog"
      >
        <el-form
          :ref="multilingualEditForm"
          :model="multilingualEditForm"
          :rules="formRules"
          label-position="left"
        >
          <el-divider content-position="left">文本设置</el-divider>
          <el-form-item label="中文文本" size="normal" prop="cn">
            <el-input v-model="multilingualEditForm.cn" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="英文文本" size="normal" prop="en">
            <el-tooltip class="item" effect="dark" content="一键翻译成英文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateEnLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','en')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.en" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="西文文本" size="normal" prop="es">
            <el-tooltip class="item" effect="dark" content="一键翻译成西文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateEsLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','spa')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.es" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="葡萄牙文文本" size="normal" prop="ptbr">
            <el-tooltip class="item" effect="dark" content="一键翻译成葡萄牙文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translatePtbrLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','pt')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.ptbr" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="俄文文本" size="normal" prop="ru">
            <el-tooltip class="item" effect="dark" content="一键翻译成俄文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateRuLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','ru')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.ru" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="德文文本" size="normal" prop="de">
            <el-tooltip class="item" effect="dark" content="一键翻译成德文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateDeLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','de')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.de" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="意大利文文本" size="normal" prop="it">
            <el-tooltip class="item" effect="dark" content="一键翻译成意大利文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateItLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','it')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.it" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="法文文本" size="normal" prop="fr">
            <el-tooltip class="item" effect="dark" content="一键翻译成法文" placement="top-start">
              <el-button size="mini" type="primary" icon="el-icon-edit" :loading="translateFrLoding" @click.prevent="getTranslationData(multilingualEditForm.cn,'zh','fra')" />
            </el-tooltip>
            <el-input v-model="multilingualEditForm.fr" type="textarea" rows="3" />
          </el-form-item>
          <el-form-item label="键名（嵌套使用 . 连接）" size="normal" prop="key">
            <el-input v-model="multilingualEditForm.key" type="textarea" rows="1" />
          </el-form-item>
          <el-divider content-position="left">使用平台</el-divider>
          <div class="platform-switcher">
            <label for="platformWeb">Web</label>
            <el-switch
              id="platformWeb"
              v-model="multilingualEditForm.platform.web"
              name="platformWeb"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformPc">PC</label>
            <el-switch
              id="platformPc"
              v-model="multilingualEditForm.platform.pc"
              name="platformPc"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformIOS">iOS</label>
            <el-switch
              id="platformIOS"
              v-model="multilingualEditForm.platform.ios"
              name="platformIOS"
            />
          </div>
          <el-divider direction="vertical" />
          <div class="platform-switcher">
            <label for="platformAndroid">安卓</label>
            <el-switch
              id="platformAndroid"
              v-model="multilingualEditForm.platform.android"
              name="platformAndroid"
            />
          </div>
          <div class="dialog-footer">
            <el-button type="primary" size="default" @click="submitEditMultilingual(multilingualEditForm, true)">确定</el-button>
            <el-button type="primary" size="default" @click="closeInsertDialog()">取消</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import { getList, updateMultilingual, insertMultilingual, deleteMultilingual,
  uploadMultilingual, updateMultilingualNeedTranslate, uploadMultilingualByPlatforms, exportMultilingual, tsLocationExportMultilingual,
  getSampleExcel, getTranslationNow } from '@/api/multilingual'
import Pagination from '@/components/Pagination'
// import KeyTag from '@/components/KeyTag'
// import KeyTagEdit from '@/components/KeyTagEdit'
import ExportCheckbox from '@/components/ExportCheckbox'
import clip from '@/utils/clipboard'
import multilingualConstant from '@/constant/multilingual'
import { transformMapToOption } from '@/utils/index'
const { status } = multilingualConstant

const statusOptions = transformMapToOption(status)

const mustInput = {
  required: true,
  message: '该项必须填入',
  trigger: 'change'
}

export default {
  name: 'Multilingual',
  components: { Pagination, ExportCheckbox },
  filters: {
    statusFilter(val) {
      return val === 0 ? '未审核' : '已审核'
    },
    booleanFilter(val) {
      return val === 0 ? '否' : '是'
    }
  },
  data() {
    this.spanMap = {}
    this.mergedColumns = ['cn', 'en', 'es', 'ptbr', 'ru','de','it','fr', 'key']
    return {
      list: null,
      total: 0,
      listQuery: {
        page: 1,
        pagesize: 10,
        id: null,
        keyword: null, // 需要进行逻辑判断
        keyLanguage: 'cn',
        is_review: null, // 简化逻辑判断直接对标数据库变量名
        uid: null,
        cn: null,
        en: null,
        es: null,
        ptbr: null,
        ru: null,
        null: null, // 查看空值特殊标识
        key: null, // 键名
        platform: null
      },
      moreListQuery: {
        es_need: null,
        es_finish: null,
        ptbr_finish: null,
        ru_finish: null
      },
      moreListQueryTags: [],
      multilingualEditForm: {
        cn: null,
        en: null,
        es: null,
        ptbr: null,
        ru: null,
        de: null,
        it: null,
        fr: null,
        key: null,
        platform: {
          web: true,
          ios: false,
          pc: false,
          android: false
        },
        uid: null,
        id: null
      },
      formRules: {
        en: mustInput,
        cn: mustInput,
        // es: mustInput,
        key: mustInput,
        platform: mustInput
      },
      exportTypes: ['Excel', 'JavaScript', 'XML', 'Strings'],
      uploadMultilingualByPlatformsConfirm: [],
      uploadMultilingualByPlatformsAccept: '.xml, .ts, .js, .strings',
      uploadMultilingualByPlatformsRatio: 'cn',
      checkedExportTypes: [],
      checkedMoreOptions: {},
      checkedTsMoreOptions: ['CN', 'EN', 'ES', 'PTBR', 'RU', 'DE', 'IT', 'FR'],
      statusOptions,
      activeUploadName: 'excel',
      activeExportName: 'general',
      isConflictManuallyShow: true,
      uploadDialogVisible: false,
      editDialogVisible: false,
      insertDialogVisible: false,
      exportDialogVisible: false,
      listLoading: true,
      jsonData: {},
      fileList: [],
      isUidShow: false,
      isUploadLoading: false,
      isExportLoading: false,
      isSampleLoading: false,
      translateEnLoding: false, // 控制英文翻译
      translateEsLoding: false, // 控制西文翻译
      translatePtbrLoding: false, // 控制葡萄牙文翻译
      translateRuLoding: false, // 控制俄文翻译
      translateDeLoding: false, // 控制德文翻译
      translateItLoding: false, // 控制意大利文翻译
      translateFrLoding: false, // 控制法文翻译
    }
  },
  computed: {
    multipleUploadQuantity: function() {
      if (this.fileList.length == null || this.fileList.length === undefined) {
        return 0
      }
      return this.fileList.length
    },
    isExportAvailable: function() {
      if (this.checkedExportTypes.length >= 1) {
        return true
      } else {
        return false
      }
    },
    isTsExportAvailable: function() {
      if (this.fileList.length === 1) {
        return true
      }
      return false
    },
    isUploadAvailable: function() {
      if (this.fileList.length <= 0) {
        return false
      }
      if (this.activeUploadName === 'types') {
        if (this.uploadMultilingualByPlatformsConfirm[0] === 'yes') {
          return true
        }
        return false
      }
      return true
    },
    isTsLocationAccessible: function() {
      if (this.activeExportName === 'ts-location' && this.fileList.length > 0) {
        return true
      }
      return false
    },
    usedPlatform: function() {
      return (val) => {
        const res = []
        if (val.is_web !== 0) {
          res.push('Web')
        }
        if (val.is_ios !== 0) {
          res.push('iOS')
        }
        if (val.is_pc !== 0) {
          res.push('PC')
        }
        if (val.is_android !== 0) {
          res.push('Android')
        }
        return res.join(', ')
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    /**
     * 获取百度翻译接口
     * @param {string} translatedText 需要翻译的文本
     * @param {string} from 翻译源语言
     * @param {string} to 翻译目标语言
     */
    async getTranslationData(translatedText, from, to) {
      if (!translatedText) {
        this.$message.error('中文文本不能为空！')
        return
      }
      // 设置加载状态
      if (to === 'en') {
        this.translateEnLoding = true
      } else if (to === 'spa') {
        this.translateEsLoding = true
      } else if (to === 'pt') {
        this.translatePtbrLoding = true
      } else if (to === 'ru') {
        this.translateRuLoding = true
      } else if (to === 'de') {
        this.translateDeLoding = true
      } else if (to === 'it') {
        this.translateItLoding = true
      } else if (to === 'fra') {
        this.translateFrLoding = true
      }
      await getTranslationNow(translatedText, from, to).then(response => {
        // 获取翻译成功
        if (response.code === 200) {
          // 获取翻译成功
          if (!response.data.error_code) {
            if (to === 'en') {
              this.multilingualEditForm.en = response.data.trans_result[0].dst
            } else if (to === 'spa') {
              this.multilingualEditForm.es = response.data.trans_result[0].dst
            } else if (to === 'pt') {
              this.multilingualEditForm.ptbr = response.data.trans_result[0].dst
            } else if (to === 'ru') {
              this.multilingualEditForm.ru = response.data.trans_result[0].dst
            } else if (to === 'de') {
              this.multilingualEditForm.de = response.data.trans_result[0].dst
            } else if (to === 'it') {
              this.multilingualEditForm.it = response.data.trans_result[0].dst
            } else if (to === 'fra') {
              this.multilingualEditForm.fr = response.data.trans_result[0].dst
            }
            this.$message({
              message: '翻译成功！',
              type: 'success'
            })
          } else {
            this.$message.error('获取翻译数据时出现错误：' + response.data.error_msg)
          }
        } else {
          this.$message.error('获取翻译数据时出现错误，请稍后再试！')
        }
      }).catch(error => {
        console.error('Error fetching translation data:', error)
        this.$message.error('获取翻译数据时出现错误，请稍后再试！')
      }).finally(() => {
        // 关闭加载状态
        if (to === 'en') {
          this.translateEnLoding = false
        } else if (to === 'spa') {
          this.translateEsLoding = false
        } else if (to === 'pt') {
          this.translatePtbrLoding = false
        } else if (to === 'ru') {
          this.translateRuLoding = false
        } else if (to === 'de') {
          this.translateDeLoding = false
        } else if (to === 'it') {
          this.translateItLoding = false
        } else if (to === 'fra') {
          this.translateFrLoding = false
        }
      })
    },

    // 对话框
    openEditDialog(row) {
      // 强制关闭其他对话框
      this.closeUploadDialog()
      this.closeInsertDialog()
      this.closeExportDialog()
      this.editDialogVisible = true
      // 数据初始化
      this.multilingualEditForm = {
        cn: row.cn,
        en: row.en,
        es: row.es,
        ptbr: row.ptbr,
        ru: row.ru,
        de: row.de,
        it: row.it,
        fr: row.fr,
        key: row.key,
        platform: {
          web: (() => { return row.is_web === 1 })(),
          pc: (() => { return row.is_pc === 1 })(),
          ios: (() => { return row.is_ios === 1 })(),
          android: (() => { return row.is_android === 1 })()
        },
        uid: this.$store.state.user.uid,
        id: row.id
      }
    },
    closeEditDialog() {
      this.editDialogVisible = false
      this.clearEditForm()
    },
    openUploadDialog() {
      this.closeEditDialog()
      this.closeInsertDialog()
      this.closeExportDialog()
      this.uploadDialogVisible = true
    },
    closeUploadDialog() {
      this.uploadDialogVisible = false
      this.clearFileList()
    },
    openInsertDialog() {
      this.closeEditDialog()
      this.closeUploadDialog()
      this.closeExportDialog()
      this.insertDialogVisible = true
      this.multilingualEditForm.uid = this.$store.state.user.uid
    },
    closeInsertDialog() {
      this.insertDialogVisible = false
      this.clearEditForm()
    },
    openExportDialog() {
      this.closeEditDialog()
      this.closeUploadDialog()
      this.closeInsertDialog()
      this.exportDialogVisible = true
    },
    closeExportDialog() {
      this.exportDialogVisible = false
      this.clearFileList()
    },

    // 访问接口操作
    findList() {
      this.listLoading = true
      // 对空字符串置空处理，修复一个由于空字符串而无法查询的bug
      for (const i in this.listQuery) {
        if (this.listQuery[i] === '' || String(this.listQuery[i]) === '') {
          this.listQuery[i] = null
        }
      }
      // 对关键词进行逻辑处理
      if (!this.listQuery.null) {
        switch (this.listQuery.keyLanguage) {
          default:
          case 'cn': {
            this.listQuery.cn = this.listQuery.keyword
            this.listQuery.en = null
            this.listQuery.es = null
            this.listQuery.ptbr = null
            this.listQuery.ru = null
            break
          }
          case 'en': {
            this.listQuery.en = this.listQuery.keyword
            this.listQuery.cn = null
            this.listQuery.es = null
            this.listQuery.ptbr = null
            this.listQuery.ru = null
            break
          }
          case 'es': {
            this.listQuery.es = this.listQuery.keyword
            this.listQuery.cn = null
            this.listQuery.en = null
            this.listQuery.ptbr = null
            this.listQuery.ru = null
            break
          }
          case 'ptbr': {
            this.listQuery.ptbr = this.listQuery.keyword
            this.listQuery.cn = null
            this.listQuery.en = null
            this.listQuery.es = null
            this.listQuery.ru = null
            break
          }
          case 'ru': {
            this.listQuery.ru = this.listQuery.keyword
            this.listQuery.cn = null
            this.listQuery.en = null
            this.listQuery.es = null
            this.listQuery.ptbr = null
            break
          }
        }
      }
      getList(Object.assign(this.listQuery, this.moreListQuery)).then(response => {
        // debugger
        this.list = response.data.res.sort((a, b) => {
          if (!a.cn || !b.cn) {
            return -1
          }
          if (a.cn !== b.cn) {
            return (a.cn).localeCompare(b.cn, 'zh')
          } else if (a.en !== b.en) {
            return (a.en).localeCompare(b.en, 'en')
          } else if (a.es !== b.es) {
            return (a.es).localeCompare(b.es, 'es')
          } else if (a.ptbr !== b.ptbr) {
            return (a.ptbr).localeCompare(b.ptbr, 'pt-BR')
          } else if (a.ru !== b.ru) {
            return (a.ru).localeCompare(b.ru, 'ru-RU')
          }
          return (a.key).localeCompare(b.key)
        })
        this.total = response.data.count
        this.listLoading = false
        this.listQuery.cn = null
        this.listQuery.en = null
        this.listQuery.es = null
        this.listQuery.ptbr = null
        this.listQuery.ru = null
        this.getSpanArr(this.list)
        this.list.forEach(val => {
          val.needTranslation = !!val.needTranslation
        })
      })
    },
    findListCustomed(obj) {
      this.listLoading = true
      obj.page = this.listQuery.page
      obj.pagesize = this.listQuery.pagesize
      getList(obj).then(response => {
        this.list = response.data.res.sort((a, b) => {
          if (!a.cn || !b.cn) {
            return -1
          }
          if (a.cn !== b.cn) {
            return (a.cn).localeCompare(b.cn, 'zh')
          } else if (a.en !== b.en) {
            return (a.en).localeCompare(b.en, 'en')
          } else if (a.es !== b.es) {
            return (a.es).localeCompare(b.es, 'es')
          } else if (a.ptbr !== b.ptbr) {
            return (a.ptbr).localeCompare(b.ptbr, 'ptbr')
          } else if (a.ru !== b.ru) {
            return (a.ru).localeCompare(b.ru, 'ru')
          }
          return (a.key).localeCompare(b.key)
        })
        this.total = response.data.count
        this.listLoading = false
        this.getSpanArr(this.list)
        this.list.forEach(val => {
          val.needTranslation = !!val.needTranslation
        })
      })
    },
    submitDeleteMultilingual(id) {
      if (typeof id !== 'number') {
        this.$message({
          message: '参数错误',
          type: 'error'
        })
        return
      }
      const deleteForm = {
        id: id,
        uid: this.$store.state.user.uid
      }
      deleteMultilingual(deleteForm).then(() => {
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.handleFilter()
      })
    },
    submitEditMultilingual(form, isInsert) {
      if (typeof isInsert !== 'boolean') {
        this.$message({
          message: '参数错误',
          type: 'error'
        })
        this.insertDialogVisible = false
        return
      }
      this.$refs[form].validate(valid => {
        if (valid) {
          if (!isInsert) {
            updateMultilingual(form).then(() => {
              this.$message({
                message: '成功',
                type: 'success'
              })
              this.editDialogVisible = false
              this.clearEditForm()
              this.handleFilterNoRePages()
              // this.handleFilter()
            }, () => {
              this.editDialogVisible = false
              this.clearEditForm()
              // 修复编辑内容后返回了第一页问题（修改为保持在当前页）
              this.handleFilterNoRePages()
              // this.handleFilter()
            })
          } else {
            insertMultilingual(form).then(() => {
              this.$message({
                message: '成功',
                type: 'success'
              })
              this.insertDialogVisible = false
              this.clearEditForm()
              this.handleFilter()
            }, () => {
              this.insertDialogVisible = false
              this.clearEditForm()
              this.handleFilter()
            })
          }
        }
      })
    },
    submitUploadMultilingualFromExcel(form) {
      const file = this.$refs.uploadMultilingualFromExcel.uploadFiles[0]
      const formData = new FormData()
      const time = new Date().getTime()
      this.isUploadLoading = true
      formData.append('file', file.raw, `import_${this.$store.state.user.uid}_${time}.xlsx`)
      formData.append('size', file.size)
      formData.append('uid', this.$store.state.user.uid)
      try {
        uploadMultilingual(formData).then(response => {
          this.isUploadLoading = false
          this.$message({
            message: `导入成功，用时${response.data.processTime}ms`,
            type: 'success'
          })
          this.closeUploadDialog()
          this.handleFilter()
        }, () => {
          this.isUploadLoading = false
        })
      } catch {
        this.isUploadLoading = false
        this.handleUploadError()
      }
    },
    submitUploadMultilingualByPlatforms(form) {
      const files = this.$refs.uploadMultilingualFromExcel.uploadFiles
      const formData = new FormData()
      const time = new Date().getTime()
      this.isUploadLoading = true
      const sizeArr = new Array(0)
      let sizeSum = 0
      for (const i in files) {
        formData.append('file', files[i].raw, `${this.$store.state.user.uid}.${time}.${files[i].name}`)
        sizeArr.push(files[i].size)
        sizeSum += files[i].size
      }
      formData.append('size', JSON.stringify(sizeArr))
      formData.append('lang', this.uploadMultilingualByPlatformsRatio)
      formData.append('sizeSum', sizeSum)
      formData.append('uid', this.$store.state.user.uid)
      try {
        uploadMultilingualByPlatforms(formData).then(() => {
          this.isUploadLoading = false
          this.$message({
            message: '导入成功',
            type: 'success'
          })
          this.closeUploadDialog()
          this.handleFilter()
        }, () => {
          this.isUploadLoading = false
        })
      } catch {
        this.isUploadLoading = false
        this.handleUploadError()
      }
    },
    submitExportMultilingual(list, ...args) {
      if (list.length <= 0) {
        this.errorMsg('导出失败, 参数为空', 5000)
        return null
      }
      this.isExportLoading = true
      exportMultilingual(list, ...args).then((res) => {
        const downloadLink = document.createElement('a')
        downloadLink.href = window.URL.createObjectURL(res)
        if (list.length > 1 || list[0] !== 'Excel') {
          downloadLink.download = `导出_${this.$store.state.user.uid}_${new Date().getTime()}.zip`
        } else {
          downloadLink.download = `导出_${this.$store.state.user.uid}_${new Date().getTime()}.xlsx`
        }
        downloadLink.click()
        window.URL.revokeObjectURL(downloadLink.href)
        this.isExportLoading = false
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        this.closeExportDialog()
      }, () => {
        this.isExportLoading = false
      })
    },
    submitTsLocationExportMultilingual(list, opts) {
      if (list.length <= 0) {
        this.errorMsg('导出失败, 参数为空', 5000)
        return null
      }
      const file = this.$refs.tsLocationExportMultilingualUpload.uploadFiles[0]
      const formData = new FormData()
      const time = new Date().getTime()
      formData.append('file', file.raw, `import_${this.$store.state.user.uid}_${time}.ts`)
      formData.append('size', file.size)
      formData.append('opts', opts)
      formData.append('uid', this.$store.state.user.uid)
      this.isExportLoading = true
      tsLocationExportMultilingual(formData).then((res) => {
        this.clearExportParameters()
        const downloadLink = document.createElement('a')
        downloadLink.href = window.URL.createObjectURL(res)
        downloadLink.download = `导出_${this.$store.state.user.uid}_${new Date().getTime()}.zip`
        downloadLink.click()
        window.URL.revokeObjectURL(downloadLink.href)
        this.isExportLoading = false
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        this.closeExportDialog()
      }, () => {
        this.isExportLoading = false
      })
    },
    submitGetSampleExcel() {
      this.isSampleLoading = true
      getSampleExcel().then((res) => {
        const downloadLink = document.createElement('a')
        downloadLink.href = window.URL.createObjectURL(res)
        downloadLink.download = `上传示例_${this.$store.state.user.uid}.xlsx`
        downloadLink.click()
        window.URL.revokeObjectURL(downloadLink.href)
        this.isSampleLoading = false
        this.$message({
          message: '下载成功',
          type: 'success'
        })
      }, () => {
        this.isSampleLoading = false
      })
    },
    changeNeedTranslate(val, id, lang) {
      // console.log(`change to ${val} at ${id}`)
      this.listLoading = true
      updateMultilingualNeedTranslate({
        val,
        id,
        lang,
        uid: this.$store.state.user.uid
      }).then((res) => {
        this.listLoading = false
        this.$message({
          message: '修改成功',
          type: 'success'
        })
      }, () => {
        this.listLoading = false
      })
    },

    // handlers
    handleFilter() {
      this.listLoading = true
      this.listQuery.null = null
      this.listQuery.page = 1
      this.findList()
    },
    // handlers, but not rolled back pages
    handleFilterNoRePages() {
      this.listLoading = true
      this.listQuery.null = null
      this.findList()
    },
    handleMoreFilterDelete(tag) {
      console.log(tag)
      const command = tag.command
      if (command === 0) {
        this.moreListQuery.es_need = null
      } else if (command === 1) {
        this.moreListQuery.es_need = null
      } else if (command === 2) {
        this.moreListQuery.es_finish = null
      } else if (command === 3) {
        this.moreListQuery.ptbr_finish = null
      } else if (command === 4) {
        this.moreListQuery.ru_finish = null
      } else if (command === 5) {
        this.moreListQuery.de_finish = null
      } else if (command === 6) {
        this.moreListQuery.it_finish = null
      } else if (command === 7) {
        this.moreListQuery.fr_finish = null
      } 
      const item = this.moreListQueryTags.indexOf(tag)
      this.moreListQueryTags.splice(item, 1)
      this.findList()
    },
    handleMoreFilterCommand(command) {
      command = parseInt(command, 10)
      if (command === 0) {
        this.moreListQuery.es_need = 0
        this.moreListQueryTags.push({
          name: '无需翻译',
          type: '',
          command: 0
        })
      } else if (command === 1) {
        this.moreListQuery.es_need = 1
        this.moreListQueryTags.push({
          name: '需要翻译',
          type: '',
          command: 1
        })
      } else if (command === 2) {
        this.moreListQuery.es_finish = 0
        this.moreListQueryTags.push({
          name: '西文未翻译',
          type: '',
          command: 2
        })
      } else if (command === 3) {
        this.moreListQuery.ptbr_finish = 0
        this.moreListQueryTags.push({
          name: '葡萄牙文未翻译',
          type: '',
          command: 3
        })
      } else if (command === 4) {
        this.moreListQuery.ru_finish = 0
        this.moreListQueryTags.push({
          name: '俄文未翻译',
          type: '',
          command: 4
        })
      } else if (command === 5) {
        this.moreListQuery.de_finish = 0
        this.moreListQueryTags.push({
          name: '德文未翻译',
          type: '',
          command: 5
        })
      }else if (command === 6) {
        this.moreListQuery.it_finish = 0
        this.moreListQueryTags.push({
          name: '意大利文未翻译',
          type: '',
          command: 6
        })
      }else if (command === 7) {
        this.moreListQuery.fr_finish = 0
        this.moreListQueryTags.push({
          name: '法文未翻译',
          type: '',
          command: 7
        })
      } else {
        // 去除所有
        this.moreListQuery = {
          es_need: null,
          es_finish: null,
          ptbr_finish: null,
          ru_finish: null,
          de_finish: null,
          it_finish: null,
          fr_finish: null,
        }
        this.moreListQueryTags = []
      }
      this.findList()
    },
    handleNullArray(arr) {
      let _arr = null
      if (!(arr instanceof Array)) {
        _arr = JSON.parse(arr)
      }
      if (_arr == null || _arr[0] == null || _arr === undefined) {
        return []
      }
      if (!(_arr instanceof Array)) {
        return this.handleNullArray(_arr)
      }
      return _arr
    },
    handleUploadRemove(file, fileList) {
      this.fileList.pop()
    },
    handleUploadChange(file, fileList) {
      this.fileList = fileList
      if (file.size > 10485760 || file.size < 0.001) {
        this.fileList.pop()
        this.errorMsg('文件大小超过10MB限制、空或非法', 5000)
      }
      let tempSize = 0
      for (const i in fileList) {
        tempSize += fileList[i].size
        if (tempSize > 10485760) {
          this.fileList.pop()
          this.errorMsg('文件总大小超过10MB限制, 无法继续上传', 5000)
        }
      }
    },
    handleUploadError(file, fileList) {
      this.errorMsg('文件上传/处理失败', 5000)
    },
    handleNullCell(lang) {
      this.listLoading = true
      this.listQuery.page = 1
      this.listQuery.cn = null
      this.listQuery.en = null
      this.listQuery.es = null
      this.listQuery.ptbr = null
      this.listQuery.ru = null
      switch (lang) {
        case 'cn': {
          this.listQuery.null = 'cn'
          this.listQuery.keyLanguage = 'cn'
          this.findList()
          break
        }
        case 'en': {
          this.listQuery.null = 'en'
          this.listQuery.keyLanguage = 'en'
          this.findList()
          break
        }
        case 'es': {
          this.listQuery.null = 'es'
          this.listQuery.keyLanguage = 'es'
          this.findList()
          break
        }
        case 'ptbr': {
          this.listQuery.null = 'ptbr'
          this.listQuery.keyLanguage = 'ptbr'
          this.findList()
          break
        }
        case 'ru': {
          this.listQuery.null = 'ru'
          this.listQuery.keyLanguage = 'ru'
          this.findList()
          break
        }
        default: {
          console.error('Invalid operation')
          return
        }
      }
    },

    // 应用等其它操作
    clearEditForm() {
      // 数据清空
      this.multilingualEditForm = {
        cn: null,
        en: null,
        es: null,
        ptbr: null,
        ru: null,
        de: null,
        it: null,
        fr: null,
        key: null,
        platform: {
          web: true,
          ios: false,
          pc: false,
          android: false
        },
        uid: this.$store.state.user.uid,
        id: null
      }
    },
    clearFileList() {
      this.fileList = []
    },
    clearExportParameters() {
      this.clearFileList()
      // this.checkedExportTypes = []
      this.checkedTsExportTypes = []
    },
    getCheckedExportTypes(val) {
      this.checkedExportTypes = val
    },
    getCheckedMoreOptions(val) {
      this.checkedMoreOptions = val
    },
    getCheckedTsExportTypes(val) {
      this.checkedTsExportTypes = val
    },
    getCheckedTsMoreOptions(val) {
      this.checkedTsMoreOptions = val
    },
    onClipboard(text, event) {
      if (text instanceof Object) {
        text = JSON.stringify(text)
      }
      if (typeof (text) === 'number') {
        text = String(text)
      }
      clip(text, event)
    },
    errorMsg(text, seconds) {
      this.$message({
        message: text,
        type: 'error',
        duration: seconds
      })
    },
    getSpanArr(data) {
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.mergedColumns.forEach(column => {
            this.spanMap[column] = {
              spanArr: [1],
              pos: 0
            }
          })
        } else {
          this.mergedColumns.forEach(column => {
            if (data[i][column] === data[i - 1][column]) {
              // 行之间西文不一样，则分开显示，不合并
              if (column === 'es' && data[i]['es'] !== data[i - 1]['es']) {
                this.spanMap[column].spanArr.push(1)
                this.spanMap[column].pos = i
              } else if (column === 'ptbr' && data[i]['ptbr'] !== data[i - 1]['ptbr']) {
                this.spanMap[column].spanArr.push(1)
                this.spanMap[column].pos = i
              } else if (column === 'ru' && data[i]['ru'] !== data[i - 1]['ru']) {
                this.spanMap[column].spanArr.push(1)
                this.spanMap[column].pos = i
              } else {
                this.spanMap[column].spanArr[this.spanMap[column].pos] += 1
                this.spanMap[column].spanArr.push(0)
              }
            } else {
              this.spanMap[column].spanArr.push(1)
              this.spanMap[column].pos = i
            }
          })
        }
      }
      // 去掉将 es 和 en 对应，因为 en 需要合并但是 es 可能不用合并（行之间西文不一样，则分开显示，不合并）
      // this.spanMap['es'] = this.spanMap['en']
      // console.log(this.spanMap)
    },
    objectSpanMethod({ column, rowIndex }) {
      if (this.spanMap[column.property]) {
        const _row = this.spanMap[column.property].spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    rowClass({ row, rowIndex }) {
      // if (!row.cn || !row.en) {
      //   return 'error-table-row'
      // } else if (!row.es || row.es === '-') {
      //   if (row.needTranslation && row.translated.es===0) {
      //     return 'error-table-row'
      //   }
      // } else if (!row.ptbr || row.ptbr === '-') {
      //   if (this.currentNeedTranslateList.ptbr[rowIndex]) {
      //     return 'error-table-row'
      //   }
      // } else if (!row.ru || row.ru === '-') {
      //   if (this.currentNeedTranslateList.ru[rowIndex]) {
      //     return 'error-table-row'
      //   }
      // }
      return 'row-el'
    }
  }
}
</script>

<style lang="scss" scoped>
  hr {
    border: solid 0.5px #f0efef;
  }
// 已全局定义
// .clearfix:after{
//   content: "";
//   display: block;
//   height: 0;
//   clear: both;
//   visibility: hidden;
// }

  .more-list-query-tags-group {
    margin-bottom: 1rem;
  }
  .more-list-query-tags {
    margin-right: 1rem;
  }

  ::v-deep .el-dialog {
    height: max-content;
    position:absolute;
    top:0;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 10em;
    margin-top: auto !important;
    margin-bottom: auto !important;
    box-sizing: border-box;
    overflow: hidden;
  }

  .clearfix {
    *zoom: 1; /* 低版本兼容 */
  }

  .editor-container{
    position: relative;
    height: 100%;
  }
  .table_more > .el-button {
    margin: 3px
  }

  .update-button, .export-button {
    float: right;
  }

  .export-button {
    margin-bottom: 2em;
  }

  .export-button:nth-of-type(2) {
    margin-right: 2em;
  }

  .el-select .el-input {
    width: 130px;
  }

  .el-input-group {
    display: inline-table;
  }

  ::v-deep .el-input-group__prepend {
    background-color: white !important;
  }

  .tips, .conflict-switch {
    margin-bottom: 1rem;
    padding-right: 5rem;
    text-align: left;
    word-break: break-all;
  }

  .uid-switch, .more-filter{
    margin-left: 10px;
    ::v-deep .el-checkbox-button__inner{
      border-radius: 20px !important;
    }
  }

  .text-button {
    width: 65px;
    border: solid 1px whitesmoke;
  }

  .upload-button {
    margin-top: 2rem;
    margin-right: 1.5rem;
    // margin-left: 12rem;
  }

  .export-info {
    color: #999;
    font-size: smaller;
  }

  ::v-deep .warning-table-row {
    background: oldlace;
  }

  ::v-deep .error-table-row {
    background: #FEF0F0;
  }

  #uploadDialog, #exportDialog {
    margin: 0 auto;
    box-sizing: border-box;
  }

  #uploadDialog {
    .el-tab-pane {
      text-align: center;
    }
    .sample-info:first-of-type {
      margin-top: -15px;
      margin-bottom: -15px;
    }
  }

  #multilingualByPlatformsUploader {
    margin: 0;
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .key_tags{
    cursor: pointer;
  }

  .platform-switcher {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    width: 5rem;
  }
  ::v-deep .el-divider--vertical {
      width: 2px;
      margin: 0 1rem;
  }

  .key_tags {
  display: block;
  overflow: hidden;
  width: fit-content;
  min-width: 50px;
  height: fit-content;
  line-height: 20px;
  margin: 0 auto;
  // margin-bottom: 10px;
  font-size: 13px;
  word-wrap: break-word;
  white-space: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -o-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

  .key_tags:hover {
    background-color: #e6e6e610;
  }

  .sub-button-row{
    display: flex;
    margin-bottom: 1rem;
    box-sizing: border-box;
    height: max-content;
    align-content: center;
  }

  // 限制弹窗的高度
  .dialog_container_box{
    //弹出层的高度
    ::v-deep .el-dialog.import-dialog{
      height: auto;
      max-height: 85vh;
      overflow-y: auto;
    }
    //弹出层里内容的高度
    ::v-deep .el-dialog__body{
      max-height: 80vh!important;
      overflow-y: auto;
    }
  }
</style>
